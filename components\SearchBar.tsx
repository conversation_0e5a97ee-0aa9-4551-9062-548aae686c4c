"use client";

import { Search } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState, FormEvent } from "react";

export default function SearchBar() {
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState("");

  const handleSearch = (e: FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // 跳转到 notices 页面并传递搜索关键词
      const searchParams = new URLSearchParams();
      searchParams.append("keyword", searchQuery.trim());
      router.push(`/notices/1?${searchParams.toString()}`);
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto">
      <form onSubmit={handleSearch} className="relative">
        <input
          type="text"
          placeholder="请输入您要查询的关键词或项目名称"
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full h-12 px-4 pr-10 rounded-md bg-white border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-800"
        />
        <button
          type="submit"
          className="absolute right-0 top-0 h-full px-4 text-blue-600 flex items-center justify-center"
        >
          <span className="hidden md:inline-block mr-2 text-blue-600 font-medium">
            搜索
          </span>
          <Search className="h-5 w-5" />
        </button>
      </form>
    </div>
  );
}
