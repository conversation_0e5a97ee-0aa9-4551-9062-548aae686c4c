class PasswordCrypto {
  private salt: string;

  constructor() {
    // 固定盐值，与后端保持一致
    this.salt = "BidSystem2025";
  }

  /**
   * 加密密码：原密码 + 盐值 + Base64编码
   * @param plainPassword 明文密码
   * @returns 加密后的密码
   */
  encrypt(plainPassword: string): string {
    const saltedPassword = plainPassword + this.salt;
    return btoa(unescape(encodeURIComponent(saltedPassword)));
  }

  /**
   * 验证加密后的密码格式是否正确
   * @param encryptedPassword 加密后的密码
   * @returns 是否为有效的加密密码
   */
  validateEncryptedPassword(encryptedPassword: string): boolean {
    try {
      const decoded = decodeURIComponent(escape(atob(encryptedPassword)));
      return decoded.endsWith(this.salt);
    } catch {
      return false;
    }
  }

  /**
   * 解密密码（仅用于调试，生产环境不建议使用）
   * @param encryptedPassword 加密后的密码
   * @returns 原始密码（去除盐值）
   */
  decrypt(encryptedPassword: string): string | null {
    try {
      const decoded = decodeURIComponent(escape(atob(encryptedPassword)));
      if (decoded.endsWith(this.salt)) {
        return decoded.slice(0, -this.salt.length);
      }
      return null;
    } catch {
      return null;
    }
  }
}

// 创建单例实例
const passwordCrypto = new PasswordCrypto();

export default passwordCrypto;
export { PasswordCrypto };
