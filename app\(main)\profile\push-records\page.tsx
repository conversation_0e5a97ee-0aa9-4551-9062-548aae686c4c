"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import userService from "@/lib/userService";
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Filter,
  Calendar,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle
} from "lucide-react";

interface PushRecord {
  id: number;
  subscription_id: number | null;
  notice_id: string | null;
  push_type: 'wechat' | 'email' | 'sms' | 'app';
  push_content: string;
  push_title: string;
  push_status: 'pending' | 'sent' | 'delivered' | 'failed';
  push_time: string;
  delivery_time: string | null;
  error_message: string | null;
  subscription_name?: string;
  notice_title?: string;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export default function PushRecordsPage() {
  const router = useRouter();
  const [records, setRecords] = useState<PushRecord[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    push_type: '',
    push_status: '',
    days: 30
  });

  useEffect(() => {
    if (!userService.isLoggedIn()) {
      router.push("/login");
      return;
    }
    loadPushRecords();
  }, [pagination.page, filters]);

  const loadPushRecords = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        days: filters.days.toString()
      });

      if (filters.push_type) {
        params.append('push_type', filters.push_type);
      }
      if (filters.push_status) {
        params.append('push_status', filters.push_status);
      }

      const response = await fetch(`/api/user/push-records?${params}`, {
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setRecords(data.data.records);
        setPagination(data.data.pagination);
      }
    } catch (error) {
      console.error('Error loading push records:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const getPushTypeIcon = (pushType: string) => {
    switch (pushType) {
      case 'email':
        return <Mail className="w-4 h-4 text-blue-600 dark:text-blue-400" />;
      case 'sms':
        return <Smartphone className="w-4 h-4 text-purple-600" />;
      case 'wechat':
        return <Bell className="w-4 h-4 text-green-600 dark:text-green-400" />;
      default:
        return <Bell className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getPushTypeText = (pushType: string) => {
    switch (pushType) {
      case 'email':
        return '邮件';
      case 'sms':
        return '短信';
      case 'wechat':
        return '微信';
      case 'app':
        return '应用';
      default:
        return pushType;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600 dark:text-red-400" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600 dark:text-yellow-400" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'sent':
        return '已发送';
      case 'delivered':
        return '已送达';
      case 'failed':
        return '发送失败';
      case 'pending':
        return '待发送';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return 'text-green-600 dark:text-green-400 bg-green-100';
      case 'failed':
        return 'text-red-600 dark:text-red-400 bg-red-100';
      case 'pending':
        return 'text-yellow-600 dark:text-yellow-400 bg-yellow-100';
      default:
        return 'text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700';
    }
  };

  if (isLoading && records.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-700 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              返回
            </button>
          </div>
          <h1 className="mt-4 text-2xl font-bold text-gray-900 dark:text-white">推送记录</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            查看您的推送通知历史记录
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Push Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">推送方式</label>
                <select
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={filters.push_type}
                  onChange={(e) => handleFilterChange('push_type', e.target.value)}
                >
                  <option value="">全部方式</option>
                  <option value="wechat">微信</option>
                  <option value="email">邮件</option>
                  <option value="sms">短信</option>
                  <option value="app">应用</option>
                </select>
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">推送状态</label>
                <select
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={filters.push_status}
                  onChange={(e) => handleFilterChange('push_status', e.target.value)}
                >
                  <option value="">全部状态</option>
                  <option value="pending">待发送</option>
                  <option value="sent">已发送</option>
                  <option value="delivered">已送达</option>
                  <option value="failed">发送失败</option>
                </select>
              </div>

              {/* Time Range Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">时间范围</label>
                <select
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={filters.days}
                  onChange={(e) => handleFilterChange('days', parseInt(e.target.value))}
                >
                  <option value={7}>最近7天</option>
                  <option value={30}>最近30天</option>
                  <option value={90}>最近90天</option>
                  <option value={365}>最近一年</option>
                </select>
              </div>

              {/* Results Count */}
              <div className="flex items-end">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  <Filter className="w-4 h-4 inline mr-1" />
                  共 {pagination.total} 条记录
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Records List */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          {records.length > 0 ? (
            <>
              <div className="divide-y divide-gray-200">
                {records.map((record) => (
                  <div key={record.id} className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          {getPushTypeIcon(record.push_type)}
                          <h3 className="text-sm font-medium text-gray-900 dark:text-white">
                            {record.push_title}
                          </h3>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.push_status)}`}>
                            {getStatusIcon(record.push_status)}
                            <span className="ml-1">{getStatusText(record.push_status)}</span>
                          </span>
                        </div>
                        
                        <div className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {record.push_content.length > 100 
                            ? `${record.push_content.substring(0, 100)}...` 
                            : record.push_content
                          }
                        </div>
                        
                        <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                          <span className="flex items-center">
                            <Calendar className="w-3 h-3 mr-1" />
                            {new Date(record.push_time).toLocaleString('zh-CN')}
                          </span>
                          {record.subscription_name && (
                            <span>来自订阅: {record.subscription_name}</span>
                          )}
                          <span>推送方式: {getPushTypeText(record.push_type)}</span>
                        </div>
                        
                        {record.error_message && (
                          <div className="mt-2 text-xs text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-2 rounded">
                            错误信息: {record.error_message}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      显示第 {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                      共 {pagination.total} 条记录
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handlePageChange(pagination.page - 1)}
                        disabled={!pagination.hasPrev}
                        className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 disabled:bg-gray-100 dark:bg-gray-700 disabled:text-gray-400"
                      >
                        上一页
                      </button>
                      <span className="px-3 py-1 text-sm text-gray-700 dark:text-gray-300">
                        第 {pagination.page} / {pagination.totalPages} 页
                      </span>
                      <button
                        onClick={() => handlePageChange(pagination.page + 1)}
                        disabled={!pagination.hasNext}
                        className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 disabled:bg-gray-100 dark:bg-gray-700 disabled:text-gray-400"
                      >
                        下一页
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <Bell className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无推送记录</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {Object.values(filters).some(v => v) 
                  ? '没有找到符合条件的推送记录，尝试调整筛选条件' 
                  : '创建订阅后将在这里显示推送记录'
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
