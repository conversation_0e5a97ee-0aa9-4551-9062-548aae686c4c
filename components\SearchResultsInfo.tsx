"use client";

import { Info, Search, Clock, MapPin, Tag } from "lucide-react";

interface SearchResultsInfoProps {
  searchParams: {
    keyword?: string | string[];
    searchType?: string | string[];
    time?: string;
    region?: string;
    province?: string | string[];
    noticeType?: string | string[];
  };
  totalResults: number;
  currentPage: number;
  totalPages: number;
}

export default function SearchResultsInfo({
  searchParams,
  totalResults,
  currentPage,
  totalPages
}: SearchResultsInfoProps) {
  const keyword = Array.isArray(searchParams.keyword)
    ? searchParams.keyword[0]
    : searchParams.keyword || "";

  const searchTypes = Array.isArray(searchParams.searchType)
    ? searchParams.searchType
    : searchParams.searchType
    ? [searchParams.searchType]
    : [];

  const provinces = Array.isArray(searchParams.province)
    ? searchParams.province
    : searchParams.province
    ? [searchParams.province]
    : [];

  const noticeTypes = Array.isArray(searchParams.noticeType)
    ? searchParams.noticeType
    : searchParams.noticeType
    ? [searchParams.noticeType]
    : [];

  const timeFilter = searchParams.time || "three_months";

  const timeLabels: { [key: string]: string } = {
    week: "近一周",
    month: "近一月", 
    three_months: "近三月",
    half_year: "近半年",
    year: "近一年"
  };

  const searchTypeLabels: { [key: string]: string } = {
    full: "全文搜索",
    title: "标题检索"
  };

  // 判断是否使用了智能搜索
  const isSmartSearch = keyword && searchTypes.length === 0;

  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
      <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
        {/* 搜索结果统计 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Search className="h-5 w-5 text-blue-600" />
            <span className="text-lg font-semibold text-gray-900 dark:text-white">
              搜索结果
            </span>
            <span className="text-sm text-gray-500">
              共找到 <span className="font-semibold text-blue-600">{totalResults}</span> 条结果
            </span>
          </div>
          <div className="text-sm text-gray-500">
            第 {currentPage} 页，共 {totalPages} 页
          </div>
        </div>

        {/* 搜索条件展示 */}
        <div className="space-y-2">
          {/* 关键词 */}
          {keyword && (
            <div className="flex items-center space-x-2">
              <Tag className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">关键词：</span>
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm font-medium">
                {keyword}
              </span>
              {isSmartSearch && (
                <span className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                  智能搜索
                </span>
              )}
            </div>
          )}

          {/* 搜索类型 */}
          {searchTypes.length > 0 && (
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">搜索类型：</span>
              <div className="flex space-x-1">
                {searchTypes.map((type) => (
                  <span
                    key={type}
                    className="px-2 py-1 bg-green-100 text-green-800 rounded text-sm"
                  >
                    {searchTypeLabels[type] || type}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* 时间范围 */}
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-600 dark:text-gray-400">时间范围：</span>
            <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-sm">
              {timeLabels[timeFilter] || timeFilter}
            </span>
          </div>

          {/* 地区筛选 */}
          {provinces.length > 0 && (
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">地区：</span>
              <div className="flex flex-wrap gap-1">
                {provinces.map((province) => (
                  <span
                    key={province}
                    className="px-2 py-1 bg-orange-100 text-orange-800 rounded text-sm"
                  >
                    {province}
                  </span>
                ))}
              </div>
            </div>
          )}

          {/* 公告类型 */}
          {noticeTypes.length > 0 && (
            <div className="flex items-center space-x-2">
              <Info className="h-4 w-4 text-gray-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">公告类型：</span>
              <div className="flex flex-wrap gap-1">
                {noticeTypes.map((type) => (
                  <span
                    key={type}
                    className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-sm"
                  >
                    {type}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 智能搜索提示 */}
        {isSmartSearch && (
          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <Info className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium">智能搜索已启用</p>
                <p className="mt-1">
                  由于您没有选择具体的搜索类型，系统自动在标题和内容中搜索关键词 "{keyword}"，
                  为您提供更全面的搜索结果。如需精确搜索，请在搜索选项中选择具体的搜索类型。
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 无结果提示 */}
        {totalResults === 0 && keyword && (
          <div className="mt-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <Info className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">未找到匹配结果</p>
                <p className="mt-1">建议您：</p>
                <ul className="mt-2 list-disc list-inside space-y-1">
                  <li>检查关键词拼写是否正确</li>
                  <li>尝试使用更通用的关键词</li>
                  <li>调整时间范围或地区筛选条件</li>
                  <li>使用智能搜索（不选择搜索类型）获得更广泛的结果</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
