import React from "react";
import Link from "next/link";
import { executeQuery } from "@/lib/db";
import { cn } from "@/lib/utils";
import SearchResultsInfo from "@/components/SearchResultsInfo";

// 类型定义
export interface Notice {
  id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  notice_third_type: string;
  notice_third_type_desc: string;
  province: string;
  publish_date: string;
  publishDate: string; // 格式化后的日期
}

export interface NoticesDisplayProps {
  currentPage: number;
  searchParams: {
    keyword?: string | string[];
    searchType?: string | string[];
    time?: string;
    region?: string;
    province?: string | string[];
    noticeType?: string | string[];
  };
}

// 格式化日期，从YYYYMMDD转为YYYY-MM-DD
function formatDate(dateString: string): string {
  if (!dateString || dateString.length !== 8) return "";
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  return `${year}-${month}-${day}`;
}

// 分页链接生成函数
export function generatePaginationLinks(
  currentPage: number,
  totalPages: number
) {
  const pagesToShow = 5;
  let startPage = Math.max(1, currentPage - Math.floor(pagesToShow / 2));
  let endPage = startPage + pagesToShow - 1;

  if (endPage > totalPages) {
    endPage = totalPages;
    startPage = Math.max(1, endPage - pagesToShow + 1);
  }

  const pages = [];
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }

  return pages;
}

// 构建查询SQL和参数
export function buildQueryParams(
  searchParams: NoticesDisplayProps["searchParams"]
) {
  const conditions = [];
  const params: any[] = [];

  // 关键词搜索
  const keyword = Array.isArray(searchParams.keyword)
    ? searchParams.keyword[0]
    : searchParams.keyword;

  // 关键词搜索逻辑 - 如果有关键词才添加搜索条件
  if (keyword && keyword.trim()) {
    const searchTypes = Array.isArray(searchParams.searchType)
      ? searchParams.searchType
      : searchParams.searchType
      ? [searchParams.searchType]
      : [];

    // 构建搜索条件
    const searchConditions = [];

    // 如果没有选择搜索类型，默认同时搜索标题和内容
    if (searchTypes.length === 0) {
      searchConditions.push("notice_title LIKE ?");
      searchConditions.push("content LIKE ?");
      params.push(`%${keyword.trim()}%`);
      params.push(`%${keyword.trim()}%`);
    } else {
      // 如果选择了标题搜索
      if (searchTypes.includes("title")) {
        searchConditions.push("notice_title LIKE ?");
        params.push(`%${keyword.trim()}%`);
      }

      // 如果选择了全文搜索
      if (searchTypes.includes("full")) {
        searchConditions.push("content LIKE ?");
        params.push(`%${keyword.trim()}%`);
      }
    }

    // 如果有搜索条件，则添加到主条件列表
    if (searchConditions.length > 0) {
      conditions.push(`(${searchConditions.join(" OR ")})`);
    }
  }
  // 注意：如果没有关键词，不添加任何搜索条件，将显示所有符合其他过滤条件的结果

  // 时间过滤
  const timeFilter = searchParams.time || "three_months";
  let dateCondition = "";

  const today = new Date();
  const startDate = new Date();

  switch (timeFilter) {
    case "week":
      startDate.setDate(today.getDate() - 7);
      break;
    case "month":
      startDate.setMonth(today.getMonth() - 1);
      break;
    case "three_months":
      startDate.setMonth(today.getMonth() - 3);
      break;
    case "half_year":
      startDate.setMonth(today.getMonth() - 6);
      break;
    case "year":
      startDate.setFullYear(today.getFullYear() - 1);
      break;
    default:
      startDate.setMonth(today.getMonth() - 3); // 默认三个月
  }

  const formatDbDate = (date: Date) => {
    return (
      date.getFullYear() +
      ("0" + (date.getMonth() + 1)).slice(-2) +
      ("0" + date.getDate()).slice(-2)
    );
  };

  dateCondition = "publish_date >= ?";
  params.push(formatDbDate(startDate));
  conditions.push(dateCondition);

  // 地区过滤
  const region = searchParams.region;
  const provinces = Array.isArray(searchParams.province)
    ? searchParams.province
    : searchParams.province
    ? [searchParams.province]
    : [];

  if (provinces.length > 0) {
    const placeholders = provinces.map(() => "?").join(", ");
    conditions.push(`province IN (${placeholders})`);
    provinces.forEach((province) => params.push(province));
  } else if (region && region !== "all") {
    // 按区域过滤，需要映射区域到省份
    const regionProvinces = getProvincesByRegion(region);
    if (regionProvinces.length > 0) {
      const placeholders = regionProvinces.map(() => "?").join(", ");
      conditions.push(`province IN (${placeholders})`);
      regionProvinces.forEach((province) => params.push(province));
    }
  }

  // 公告类型过滤
  const noticeTypes = Array.isArray(searchParams.noticeType)
    ? searchParams.noticeType
    : searchParams.noticeType
    ? [searchParams.noticeType]
    : [];

  if (noticeTypes.length > 0) {
    const placeholders = noticeTypes.map(() => "?").join(", ");
    conditions.push(`notice_third_type_desc IN (${placeholders})`);
    noticeTypes.forEach((type) => params.push(type));
  }

  // 构建WHERE子句
  const whereClause =
    conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

  return { whereClause, params };
}

// 根据区域ID获取对应的省份列表
function getProvincesByRegion(regionId: string): string[] {
  const regionMap: { [key: string]: string[] } = {
    north: ["北京", "天津", "河北", "山西", "内蒙古"],
    east: ["上海", "江苏", "浙江", "安徽", "福建", "江西", "山东"],
    south: ["广东", "广西", "海南"],
    central: ["河南", "湖北", "湖南"],
    southwest: ["重庆", "四川", "贵州", "云南", "西藏"],
    northwest: ["陕西", "甘肃", "青海", "宁夏", "新疆"],
    northeast: ["辽宁", "吉林", "黑龙江"],
  };

  return regionMap[regionId] || [];
}

// 保持搜索参数并更新页码
export function getPageUrl(
  page: number,
  searchParams: NoticesDisplayProps["searchParams"]
) {
  const url = new URL(`/notices/${page}`, "http://example.com");

  // 添加关键词
  if (searchParams.keyword) {
    const keywords = Array.isArray(searchParams.keyword)
      ? searchParams.keyword
      : [searchParams.keyword];

    keywords.forEach((k) => k && url.searchParams.append("keyword", k));
  }

  // 添加搜索类型
  if (searchParams.searchType) {
    const types = Array.isArray(searchParams.searchType)
      ? searchParams.searchType
      : [searchParams.searchType];

    types.forEach((t) => t && url.searchParams.append("searchType", t));
  }

  // 添加时间过滤器
  if (searchParams.time) {
    url.searchParams.set("time", searchParams.time);
  }

  // 添加地区
  if (searchParams.region) {
    url.searchParams.set("region", searchParams.region);
  }

  // 添加省份
  if (searchParams.province) {
    const provinces = Array.isArray(searchParams.province)
      ? searchParams.province
      : [searchParams.province];

    provinces.forEach((p) => p && url.searchParams.append("province", p));
  }

  // 添加公告类型
  if (searchParams.noticeType) {
    const noticeTypes = Array.isArray(searchParams.noticeType)
      ? searchParams.noticeType
      : [searchParams.noticeType];

    noticeTypes.forEach((t) => t && url.searchParams.append("noticeType", t));
  }

  return url.pathname + url.search;
}

export default async function NoticesDisplay({
  currentPage,
  searchParams,
}: NoticesDisplayProps) {
  const pageSize = 25;

  // 构建查询条件
  const { whereClause, params: queryParams } = buildQueryParams(searchParams);

  // 获取总数
  const countSql = `SELECT COUNT(*) as total FROM trading_notices ${whereClause}`;
  const totalResult = await executeQuery<any[]>(countSql, queryParams);
  const total = totalResult[0].total;
  const totalPages = Math.ceil(total / pageSize);

  // 获取分页数据
  const offset = (currentPage - 1) * pageSize;
  const dataSql = `
    SELECT 
      id, 
      notice_id, 
      notice_title, 
      notice_type_desc, 
      notice_third_type_desc,
      province, 
      publish_date 
    FROM trading_notices 
    ${whereClause}
    ORDER BY publish_date DESC, id DESC 
    LIMIT ${pageSize} OFFSET ${offset}
  `;

  const notices = await executeQuery<Notice[]>(dataSql, queryParams);

  // 格式化日期
  const formattedNotices = notices.map((notice) => ({
    ...notice,
    publishDate: formatDate(notice.publish_date),
  }));

  // 生成分页链接
  const paginationLinks = generatePaginationLinks(currentPage, totalPages);

  return (
    <>
      {/* 搜索结果信息 */}
      <SearchResultsInfo
        searchParams={searchParams}
        totalResults={total}
        currentPage={currentPage}
        totalPages={totalPages}
      />

      {/* 结果列表 */}
      <div className="bg-white dark:bg-gray-800 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col md:flex-row">
            {/* 左侧列 - 公告列表 */}
            <div className="w-full md:w-3/4 md:pr-4">
              <div className="divide-y">
                {formattedNotices.map((notice) => (
                  <div
                    key={notice.id}
                    className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <Link
                      href={
                        notice.notice_third_type_desc === "招标计划" ||
                        notice.notice_third_type === "A14"
                          ? `/notices/plan/${notice.id}`
                          : notice.notice_third_type_desc === "招标公告" ||
                            notice.notice_third_type === "A12"
                          ? `/bid-notice/${notice.id}`
                          : notice.notice_third_type_desc === "中标结果" ||
                            notice.notice_third_type === "A06"
                          ? `/bid-result/${notice.id}`
                          : `/notices/detail/${notice.id}`
                      }
                      className="block"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex flex-1 items-center text-sm">
                          <h2 className="text-sm md:text-[14px] font-medium text-gray-900 dark:text-white line-clamp-1 mr-2">
                            {notice.notice_title}
                          </h2>
                        </div>
                        <div className="flex items-center flex-shrink-0 ml-2 space-x-4">
                          <span
                            className={cn(
                              "px-2 py-1 text-[14px] text-white rounded mr-2",
                              notice.notice_third_type_desc === "中标结果"
                                ? "bg-red-500"
                                : notice.notice_third_type_desc === "招标公告"
                                ? "bg-blue-500"
                                : notice.notice_third_type_desc === "变更公告"
                                ? "bg-orange-500"
                                : notice.notice_third_type_desc === "招标计划"
                                ? "bg-green-500"
                                : "bg-yellow-500"
                            )}
                          >
                            {notice.notice_third_type_desc}
                          </span>
                          <span className="px-2 py-1 text-[14px] mr-2 text-center w-[80px]">
                            {notice.province}
                          </span>
                          <span className="text-[14px] text-gray-500 dark:text-gray-400">
                            {notice.publishDate}
                          </span>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>

              {/* 分页 */}
              <div className="flex justify-center items-center p-4">
                <div className="flex space-x-1">
                  {currentPage > 1 && (
                    <Link
                      href={getPageUrl(currentPage - 1, searchParams)}
                      className="px-3 py-1 bg-white dark:bg-gray-700 border text-gray-700 dark:text-gray-200 rounded"
                    >
                      上一页
                    </Link>
                  )}

                  {paginationLinks.map((page) => (
                    <Link
                      key={page}
                      href={getPageUrl(page, searchParams)}
                      className={cn(
                        "px-3 py-1 rounded",
                        currentPage === page
                          ? "bg-blue-500 text-white"
                          : "bg-white dark:bg-gray-700 border text-gray-700 dark:text-gray-200"
                      )}
                    >
                      {page}
                    </Link>
                  ))}

                  {currentPage < totalPages && (
                    <Link
                      href={getPageUrl(currentPage + 1, searchParams)}
                      className="px-3 py-1 bg-white dark:bg-gray-700 border text-gray-700 dark:text-gray-200 rounded"
                    >
                      下一页
                    </Link>
                  )}
                </div>
              </div>
            </div>

            {/* 右侧列 */}
            <div className="w-full md:w-1/4 md:pl-4 space-y-4 mt-4 md:mt-0">
              {/* 搜索技巧 - 上部分 */}
              <div className="bg-white dark:bg-gray-700 rounded-md border p-4">
                <div className="flex items-center gap-2 mb-2">
                  <svg
                    className="h-5 w-5 text-blue-500"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                  </svg>
                  <h3 className="font-medium text-gray-800 dark:text-gray-200">
                    搜索技巧推荐
                  </h3>
                </div>
                <ul className="text-sm space-y-2 text-gray-700 dark:text-gray-300">
                  <li>1.关键词请尽量简洁明了；</li>
                  <li>
                    2.查询某城市的项目，可以这么填写：
                    <br />
                    <span className="ml-2">
                      北京 工程{" "}
                      <span className="text-gray-500">(中间用空格隔开)</span>；
                    </span>
                  </li>
                  <li>3.企业名称有简称和全称之分，您可以适当调整；</li>
                  <li>4.请尽量避免使用行业俗语，系统可能无法识别。</li>
                </ul>
              </div>

              {/* 热门类别 - 下部分 */}
              <div className="bg-white dark:bg-gray-700 rounded-md border p-4 shadow-sm">
                <div className="flex items-center gap-2 mb-4 border-b pb-2">
                  <svg
                    className="h-5 w-5 text-red-500"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M12 2L2 7l10 5 10-5-10-5z" />
                    <path d="M2 17l10 5 10-5" />
                    <path d="M2 12l10 5 10-5" />
                  </svg>
                  <h3 className="font-medium text-gray-800 dark:text-gray-200 text-lg">
                    招标热门类别
                  </h3>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  {[
                    { name: "建筑工程", count: 3528, color: "bg-red-500" },
                    { name: "设备采购", count: 2871, color: "bg-blue-500" },
                    { name: "医疗器械", count: 2145, color: "bg-green-500" },
                    { name: "信息技术", count: 1982, color: "bg-yellow-500" },
                    { name: "环保能源", count: 1647, color: "bg-purple-500" },
                    { name: "交通运输", count: 1523, color: "bg-indigo-500" },
                    { name: "地铁", count: 1357, color: "bg-pink-500" },
                    { name: "作业车", count: 1129, color: "bg-orange-500" },
                  ].map((category, index) => (
                    <Link
                      key={index}
                      href={`/notices/1?keyword=${encodeURIComponent(
                        category.name
                      )}`}
                      className="group flex flex-col items-center p-2 rounded-lg border border-gray-100 hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors"
                    >
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center gap-1.5">
                          <span
                            className={`h-3 w-3 ${category.color} rounded-full`}
                          ></span>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                            {category.name}
                          </span>
                        </div>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {category.count.toLocaleString()}
                        </span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
