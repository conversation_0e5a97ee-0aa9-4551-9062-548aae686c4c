"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Search } from "lucide-react";
import RegionSelector from "./RegionSelector";

// 搜索类型选项
const searchTypeOptions = [
  { id: "full", name: "全文搜索" },
  { id: "title", name: "标题检索" },
];

// 时间选项
const timeOptions = [
  { id: "week", name: "近一周" },
  { id: "month", name: "近一月" },
  { id: "three_months", name: "近三月" },
  { id: "half_year", name: "近半年" },
  { id: "year", name: "近一年" },
];

// 公告类型选项
const noticeTypeOptions = [
  { id: "招标公告", name: "招标公告" },
  { id: "中标结果", name: "中标结果" },
  { id: "变更公告", name: "变更公告" },
  { id: "招标计划", name: "招标计划" },
];

// 地区选项
const regionOptions = [
  { id: "all", name: "全国" },
  {
    id: "north",
    name: "华北",
    subOptions: ["北京", "天津", "河北", "山西", "内蒙古"],
  },
  {
    id: "east",
    name: "华东",
    subOptions: ["上海", "江苏", "浙江", "安徽", "福建", "江西", "山东"],
  },
  { id: "south", name: "华南", subOptions: ["广东", "广西", "海南"] },
  { id: "central", name: "华中", subOptions: ["河南", "湖北", "湖南"] },
  {
    id: "southwest",
    name: "西南",
    subOptions: ["重庆", "四川", "贵州", "云南", "西藏"],
  },
  {
    id: "northwest",
    name: "西北",
    subOptions: ["陕西", "甘肃", "青海", "宁夏", "新疆"],
  },
  { id: "northeast", name: "东北", subOptions: ["辽宁", "吉林", "黑龙江"] },
];

interface SearchOptionsProps {
  baseUrl: string;
  initialKeyword?: string;
  initialSearchTypes?: string[];
  initialTimeFilter?: string;
  initialRegion?: string;
  initialProvinces?: string[];
  initialNoticeTypes?: string[];
}

export default function SearchOptions({
  baseUrl = "/search",
  initialKeyword = "",
  initialSearchTypes = [],
  initialTimeFilter = "three_months",
  initialRegion = "all",
  initialProvinces = [],
  initialNoticeTypes = [],
}: SearchOptionsProps) {
  const router = useRouter();

  const [keyword, setKeyword] = useState(initialKeyword);
  const [searchTypes, setSearchTypes] = useState<string[]>(initialSearchTypes);
  const [timeFilter, setTimeFilter] = useState(initialTimeFilter);
  const [selectedRegion, setSelectedRegion] = useState(initialRegion);
  const [selectedProvinces, setSelectedProvinces] = useState<string[]>(initialProvinces);
  const [noticeTypes, setNoticeTypes] = useState<string[]>(initialNoticeTypes);

  // 切换搜索类型（多选）
  const toggleSearchType = (typeId: string) => {
    if (searchTypes.includes(typeId)) {
      setSearchTypes(searchTypes.filter((t) => t !== typeId));
    } else {
      setSearchTypes([...searchTypes, typeId]);
    }
  };

  // 切换公告类型（多选）
  const toggleNoticeType = (typeId: string) => {
    if (noticeTypes.includes(typeId)) {
      setNoticeTypes(noticeTypes.filter((t) => t !== typeId));
    } else {
      setNoticeTypes([...noticeTypes, typeId]);
    }
  };

  // 设置时间过滤器（单选）
  const setTime = (timeId: string) => {
    setTimeFilter(timeId);
  };

  // 一键智能搜索 - 当用户没有选择搜索类型时的快捷操作
  const handleSmartSearch = () => {
    // 如果有关键词但没有选择搜索类型，自动选择智能搜索
    if (keyword && keyword.trim() && searchTypes.length === 0) {
      // 自动选择全文搜索和标题搜索
      setSearchTypes(["full", "title"]);
      // 延迟执行搜索，确保状态更新
      setTimeout(() => {
        handleSearch();
      }, 100);
    } else {
      // 无关键词或已选择搜索类型，直接搜索
      handleSearch();
    }
  };

  // 点击搜索按钮，执行搜索
  const handleSearch = () => {
    // 允许无关键词搜索，显示所有结果

    // 构建搜索URL
    const searchParams = new URLSearchParams();

    // 添加关键词
    if (keyword) {
      searchParams.append("keyword", keyword);
    }

    // 添加搜索类型
    searchTypes.forEach((type) => {
      searchParams.append("searchType", type);
    });

    // 添加时间过滤器
    if (timeFilter) {
      searchParams.append("time", timeFilter);
    }

    // 添加地区过滤器
    if (selectedRegion && selectedRegion !== "all") {
      searchParams.append("region", selectedRegion);
    }

    // 添加省份过滤器
    selectedProvinces.forEach((province) => {
      searchParams.append("province", province);
    });

    // 添加公告类型过滤器
    noticeTypes.forEach((type) => {
      searchParams.append("noticeType", type);
    });

    // 导航到搜索结果页面
    const searchPath = `${baseUrl}/1?${searchParams.toString()}`;
    router.push(searchPath);
  };

  // 接收来自RegionSelector的更新
  const handleRegionChange = (region: string, provinces: string[]) => {
    setSelectedRegion(region);
    setSelectedProvinces(provinces);
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-b pt-12">
      <div className="max-w-7xl mx-auto px-4 py-4 sm:px-6 lg:px-8">
        <div className="flex flex-col space-y-4">
          {/* 搜索栏 */}
          <div className="relative flex items-center">
            <div className="pl-3 absolute z-10">
              <Search className="h-5 w-5 text-gray-500" />
            </div>
            <input
              type="text"
              placeholder="请输入项目关键词（可选，不输入将显示所有结果）"
              className="w-full py-3 pl-10 pr-20 border rounded-md text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm sm:text-base"
              value={keyword}
              onChange={(e) => setKeyword(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSmartSearch();
                }
              }}
            />
            <button
              className="absolute right-0 bg-blue-500 hover:bg-blue-600 text-white px-4 sm:px-6 py-3 rounded-r-md h-full transition-colors text-sm sm:text-base"
              onClick={handleSmartSearch}
            >
              搜索
            </button>
          </div>

          {/* 移动端搜索提示 */}
          <div className="block sm:hidden text-xs text-gray-500 px-2">
            💡 不输入关键词将显示所有结果，输入关键词且未选择搜索类型时将同时搜索标题和内容
          </div>

          {/* 过滤选项 */}
          <div className="flex flex-col space-y-3 sm:space-y-2">
            {/* 搜索类型 - 允许多选 */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
              <span className="text-gray-700 dark:text-gray-300 text-sm font-medium sm:w-20 flex-shrink-0">
                搜索类型
              </span>
              <div className="flex flex-col sm:flex-row sm:flex-wrap gap-2 sm:items-center">
                <div className="flex flex-wrap gap-2">
                  {searchTypeOptions.map((option) => (
                    <button
                      key={option.id}
                      onClick={() => toggleSearchType(option.id)}
                      className={`px-3 py-2 text-sm border rounded-md transition-colors ${searchTypes.includes(option.id)
                        ? "bg-blue-500 text-white border-blue-500"
                        : "text-gray-700 hover:bg-gray-50 border-gray-300 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                        }`}
                    >
                      {option.name}
                    </button>
                  ))}
                </div>
                {searchTypes.length === 0 && keyword && (
                  <div className="hidden sm:flex items-center ml-2 px-2 py-1 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-700">
                    <span>💡 智能搜索：将同时搜索标题和内容</span>
                  </div>
                )}
                {searchTypes.length === 0 && !keyword && (
                  <span className="hidden sm:inline text-xs text-gray-500 ml-2">
                    未选择时将同时搜索标题和内容
                  </span>
                )}
              </div>
            </div>

            {/* 发布时间 - 只能单选 */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
              <span className="text-gray-700 dark:text-gray-300 text-sm font-medium sm:w-20 flex-shrink-0">
                发布时间
              </span>
              <div className="flex flex-wrap gap-2">
                {timeOptions.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => setTime(option.id)}
                    className={`px-3 py-2 text-sm border rounded-md transition-colors ${option.id === timeFilter
                      ? "bg-blue-500 text-white border-blue-500"
                      : "text-gray-700 hover:bg-gray-50 border-gray-300 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                      }`}
                  >
                    {option.name}
                  </button>
                ))}
              </div>
            </div>

            {/* 公告类型 - 允许多选 */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
              <span className="text-gray-700 dark:text-gray-300 text-sm font-medium sm:w-20 flex-shrink-0">
                公告类型
              </span>
              <div className="flex flex-wrap gap-2">
                {noticeTypeOptions.map((option) => (
                  <button
                    key={option.id}
                    onClick={() => toggleNoticeType(option.id)}
                    className={`px-3 py-2 text-sm border rounded-md transition-colors ${noticeTypes.includes(option.id)
                      ? "bg-blue-500 text-white border-blue-500"
                      : "text-gray-700 hover:bg-gray-50 border-gray-300 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
                      }`}
                  >
                    {option.name}
                  </button>
                ))}
              </div>
            </div>

            {/* 项目地区 - 使用下拉菜单多选 */}
            <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0">
              <span className="text-gray-700 dark:text-gray-300 text-sm font-medium sm:w-20 flex-shrink-0">
                项目地区
              </span>
              <div className="flex flex-wrap gap-2">
                <RegionSelector
                  options={regionOptions}
                  selectedRegion={selectedRegion}
                  selectedProvinces={selectedProvinces}
                  baseUrl="#"
                  onApply={handleRegionChange}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
