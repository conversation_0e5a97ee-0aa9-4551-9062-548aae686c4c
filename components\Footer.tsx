import Link from "next/link";
import Image from "next/image";
import { PhoneCall, Mail, MapPin } from "lucide-react";
import { ThemeToggle } from "@/components/theme-toggle";

export default function Footer() {
  return (
    <footer className="bg-blue-600 dark:bg-blue-900 text-white hidden md:block">
      {/* Main footer content */}
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-medium mb-4">关于我们</h3>
            <div className="flex items-center mb-2">
              <PhoneCall className="h-5 w-5 mr-2 text-blue-200 dark:text-blue-300" />
              <span>************</span>
            </div>
            <div className="flex items-center mb-2">
              <Mail className="h-5 w-5 mr-2 text-blue-200 dark:text-blue-300" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-start mb-4">
              <MapPin className="h-5 w-5 mr-2 text-blue-200 dark:text-blue-300 flex-shrink-0 mt-1" />
              <span>北京市朝阳区建国路88号现代国际大厦A座25层</span>
            </div>
            <div className="flex space-x-4">
              <Image
                src="https://picsum.photos/80/80"
                alt="WeChat QR Code"
                width={80}
                height={80}
                className="bg-white p-1 rounded"
              />
              <Image
                src="https://picsum.photos/80/80"
                alt="App QR Code"
                width={80}
                height={80}
                className="bg-white p-1 rounded"
              />
            </div>
          </div>

          {/* Business Areas */}
          <div>
            <h3 className="text-lg font-medium mb-4">业务板块</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/services/bidding"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  招标服务
                </Link>
              </li>
              <li>
                <Link
                  href="/services/consulting"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  咨询规划
                </Link>
              </li>
              <li>
                <Link
                  href="/services/evaluation"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  评估服务
                </Link>
              </li>
              <li>
                <Link
                  href="/services/agency"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  招标代理
                </Link>
              </li>
              <li>
                <Link
                  href="/services/software"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  软件开发
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-medium mb-4">客服中心</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/support/guide"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  新手指南
                </Link>
              </li>
              <li>
                <Link
                  href="/support/faq"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  常见问题
                </Link>
              </li>
              <li>
                <Link
                  href="/support/contact"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  联系客服
                </Link>
              </li>
              <li>
                <Link
                  href="/profile/feedback/new"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  意见反馈
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-lg font-medium mb-4">法律法规</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/legal/terms"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  服务条款
                </Link>
              </li>
              <li>
                <Link
                  href="/legal/privacy"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  隐私政策
                </Link>
              </li>
              <li>
                <Link
                  href="/legal/agreement"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  用户协议
                </Link>
              </li>
              <li>
                <Link
                  href="/legal/copyright"
                  className="text-blue-100 hover:text-white transition-colors"
                >
                  版权声明
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Bottom footer */}
      <div className="border-t border-blue-500 bg-blue-700">
        <div className="max-w-7xl mx-auto px-4 py-6 sm:px-6 lg:px-8">
          <div className="md:flex md:items-center md:justify-between">
            <div className="text-sm text-blue-200">
              <p>
                © {new Date().getFullYear()} 招标通 版权所有
                ICP证：京ICP备12345678号
              </p>
            </div>
            <div className="mt-4 md:mt-0">
              <div className="flex space-x-6 items-center">
                <Link
                  href="/sitemap"
                  className="text-sm text-blue-200 hover:text-white"
                >
                  网站地图
                </Link>
                <Link
                  href="/profile/feedback/new"
                  className="text-sm text-blue-200 hover:text-white"
                >
                  意见反馈
                </Link>
                <Link
                  href="/help"
                  className="text-sm text-blue-200 hover:text-white"
                >
                  帮助中心
                </Link>
                <div className="text-sm text-blue-200 hover:text-white cursor-pointer">
                  <ThemeToggle />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
