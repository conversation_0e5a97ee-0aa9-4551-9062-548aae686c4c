import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

// Types for favorites
interface Favorite {
  id: number;
  user_id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  publish_date: string;
  site_name: string;
  created_at: string;
  notice_content?: string;
  publish_agency?: string;
}

// Helper function to verify JWT token and get user ID
async function getUserFromToken(request: NextRequest): Promise<number | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    return decoded.user_id || decoded.id;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// GET - Fetch user's favorites
export async function GET(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const notice_type = searchParams.get('notice_type') || '';

    const offset = (page - 1) * limit;

    // Build query with filters
    let whereClause = 'WHERE uf.user_id = ?';
    const params: any[] = [userId];

    if (search) {
      whereClause += ' AND (tn.notice_title LIKE ? OR tn.publish_agency LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    if (notice_type) {
      whereClause += ' AND tn.notice_type_desc = ?';
      params.push(notice_type);
    }

    // Get favorites with notice details
    const favorites = await executeQuery<Favorite[]>(
      `SELECT 
        uf.id, uf.user_id, uf.notice_id, uf.created_at,
        tn.notice_title, tn.notice_type_desc, tn.publish_date,
        tn.site_name, tn.publish_agency, tn.content as notice_content
      FROM user_favorites uf
      LEFT JOIN trading_notices tn ON uf.notice_id = tn.notice_id
      ${whereClause}
      ORDER BY uf.created_at DESC
      LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    // Get total count for pagination
    const totalResult = await executeQuery<any[]>(
      `SELECT COUNT(*) as total
      FROM user_favorites uf
      LEFT JOIN trading_notices tn ON uf.notice_id = tn.notice_id
      ${whereClause}`,
      params
    );

    const total = totalResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        favorites,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching favorites:', error);
    return NextResponse.json(
      { error: 'Failed to fetch favorites' },
      { status: 500 }
    );
  }
}

// POST - Add to favorites
export async function POST(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { notice_id } = body;

    if (!notice_id) {
      return NextResponse.json(
        { error: 'Notice ID is required' },
        { status: 400 }
      );
    }

    // Check if already favorited
    const existing = await executeQuery<any[]>(
      'SELECT id FROM user_favorites WHERE user_id = ? AND notice_id = ?',
      [userId, notice_id]
    );

    if (existing.length > 0) {
      return NextResponse.json(
        { error: 'Already in favorites' },
        { status: 400 }
      );
    }

    // Add to favorites
    await executeQuery(
      'INSERT INTO user_favorites (user_id, notice_id) VALUES (?, ?)',
      [userId, notice_id]
    );

    return NextResponse.json({
      success: true,
      message: 'Added to favorites successfully'
    });
  } catch (error) {
    console.error('Error adding to favorites:', error);
    return NextResponse.json(
      { error: 'Failed to add to favorites' },
      { status: 500 }
    );
  }
}

// DELETE - Remove from favorites
export async function DELETE(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const notice_id = searchParams.get('notice_id');
    const favorite_id = searchParams.get('id');

    if (!notice_id && !favorite_id) {
      return NextResponse.json(
        { error: 'Notice ID or Favorite ID is required' },
        { status: 400 }
      );
    }

    let query = 'DELETE FROM user_favorites WHERE user_id = ?';
    const params: any[] = [userId];

    if (favorite_id) {
      query += ' AND id = ?';
      params.push(parseInt(favorite_id));
    } else if (notice_id) {
      query += ' AND notice_id = ?';
      params.push(notice_id);
    }

    const result = await executeQuery(query, params);

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { error: 'Favorite not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Removed from favorites successfully'
    });
  } catch (error) {
    console.error('Error removing from favorites:', error);
    return NextResponse.json(
      { error: 'Failed to remove from favorites' },
      { status: 500 }
    );
  }
}
