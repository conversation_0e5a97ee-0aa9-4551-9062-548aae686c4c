import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

// Types for browsing history
interface BrowsingHistory {
  id: number;
  user_id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  publish_date: string;
  site_name: string;
  view_count: number;
  first_viewed_at: string;
  last_viewed_at: string;
  notice_content?: string;
  publish_agency?: string;
}

// Helper function to verify JWT token and get user ID
async function getUserFromToken(request: NextRequest): Promise<number | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    return decoded.user_id || decoded.id;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// GET - Fetch user's browsing history
export async function GET(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const search = searchParams.get('search') || '';
    const notice_type = searchParams.get('notice_type') || '';
    const days = parseInt(searchParams.get('days') || '30');

    const offset = (page - 1) * limit;

    // Build query with filters
    let whereClause = 'WHERE uh.user_id = ? AND uh.last_viewed_at >= DATE_SUB(NOW(), INTERVAL ? DAY)';
    const params: any[] = [userId, days];

    if (search) {
      whereClause += ' AND (tn.notice_title LIKE ? OR tn.publish_agency LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    if (notice_type) {
      whereClause += ' AND tn.notice_type_desc = ?';
      params.push(notice_type);
    }

    // Get browsing history with notice details
    const history = await executeQuery<BrowsingHistory[]>(
      `SELECT 
        uh.id, uh.user_id, uh.notice_id, uh.view_count,
        uh.first_viewed_at, uh.last_viewed_at,
        tn.notice_title, tn.notice_type_desc, tn.publish_date,
        tn.site_name, tn.publish_agency, tn.content as notice_content
      FROM user_browsing_history uh
      LEFT JOIN trading_notices tn ON uh.notice_id = tn.notice_id
      ${whereClause}
      ORDER BY uh.last_viewed_at DESC
      LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    // Get total count for pagination
    const totalResult = await executeQuery<any[]>(
      `SELECT COUNT(*) as total
      FROM user_browsing_history uh
      LEFT JOIN trading_notices tn ON uh.notice_id = tn.notice_id
      ${whereClause}`,
      params
    );

    const total = totalResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        history,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching browsing history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch browsing history' },
      { status: 500 }
    );
  }
}

// POST - Add/Update browsing history
export async function POST(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { notice_id } = body;

    if (!notice_id) {
      return NextResponse.json(
        { error: 'Notice ID is required' },
        { status: 400 }
      );
    }

    // Check if already exists
    const existing = await executeQuery<any[]>(
      'SELECT id, view_count FROM user_browsing_history WHERE user_id = ? AND notice_id = ?',
      [userId, notice_id]
    );

    if (existing.length > 0) {
      // Update existing record
      await executeQuery(
        `UPDATE user_browsing_history 
         SET view_count = view_count + 1, last_viewed_at = CURRENT_TIMESTAMP 
         WHERE user_id = ? AND notice_id = ?`,
        [userId, notice_id]
      );
    } else {
      // Insert new record
      await executeQuery(
        `INSERT INTO user_browsing_history (user_id, notice_id, view_count, first_viewed_at, last_viewed_at) 
         VALUES (?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [userId, notice_id]
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Browsing history updated successfully'
    });
  } catch (error) {
    console.error('Error updating browsing history:', error);
    return NextResponse.json(
      { error: 'Failed to update browsing history' },
      { status: 500 }
    );
  }
}

// DELETE - Clear browsing history
export async function DELETE(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const notice_id = searchParams.get('notice_id');
    const history_id = searchParams.get('id');
    const clear_all = searchParams.get('clear_all') === 'true';
    const days = parseInt(searchParams.get('days') || '0');

    if (clear_all) {
      // Clear all history or history older than specified days
      let query = 'DELETE FROM user_browsing_history WHERE user_id = ?';
      const params = [userId];

      if (days > 0) {
        query += ' AND last_viewed_at < DATE_SUB(NOW(), INTERVAL ? DAY)';
        params.push(days);
      }

      const result = await executeQuery(query, params);
      return NextResponse.json({
        success: true,
        message: `Cleared ${(result as any).affectedRows} browsing history records`
      });
    }

    if (!notice_id && !history_id) {
      return NextResponse.json(
        { error: 'Notice ID or History ID is required' },
        { status: 400 }
      );
    }

    let query = 'DELETE FROM user_browsing_history WHERE user_id = ?';
    const params: any[] = [userId];

    if (history_id) {
      query += ' AND id = ?';
      params.push(parseInt(history_id));
    } else if (notice_id) {
      query += ' AND notice_id = ?';
      params.push(notice_id);
    }

    const result = await executeQuery(query, params);

    if ((result as any).affectedRows === 0) {
      return NextResponse.json(
        { error: 'History record not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'History record deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting browsing history:', error);
    return NextResponse.json(
      { error: 'Failed to delete browsing history' },
      { status: 500 }
    );
  }
}
