import { Suspense } from "react";
import { executeQuery } from "@/lib/db";
import HeroSection from "@/components/HeroSection";
import CategoryGrid from "@/components/CategoryGrid";
import HotTopics from "@/components/IndustryCategories";
import LatestNotices from "@/components/LatestNotices";
import ServiceFeatures from "@/components/ServiceFeatures";
import StatisticsSection from "@/components/StatisticsSection";
import IndustryNavigation from "@/components/IndustryNavigation";
import MobileNavigation from "@/components/MobileNavigation";

export const revalidate = 60;

// Types for our data
interface TradingNotice {
  id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  publish_date: string;
  site_name: string;
  created_at: string;
  project_code: string;
}

// Function to fetch data from the MySQL database
async function getTradingNotices() {
  // Query to get trading notices sorted by publish_date
  return await executeQuery<TradingNotice[]>(
    `SELECT 
      id, 
      notice_id, 
      notice_title, 
      notice_type_desc, 
      publish_date, 
      site_name, 
      created_at,
      project_code
    FROM 
      trading_notices 
    ORDER BY 
      created_at DESC, 
      notice_id DESC 
    LIMIT 500`
  );
}

// Format date from 'yyyyMMdd' to 'yyyy-MM-dd' format
function formatPublishDate(dateStr: string) {
  if (!dateStr || dateStr.length !== 8) return "Unknown date";

  const year = dateStr.substring(0, 4);
  const month = dateStr.substring(4, 6);
  const day = dateStr.substring(6, 8);

  return `${year}-${month}-${day}`;
}

export default function Home() {
  return (
    <div className="pb-16 md:pb-0"> {/* pb-16 for mobile navigation spacing */}
      {/* Hero Section with Search */}
      <HeroSection />
      
      {/* Category Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <CategoryGrid />
        
        {/* Hot Topics - 只在桌面版显示 */}
        <div className="hidden md:block">
          <HotTopics />
        </div>
        
        {/* Latest Notices Section - Client Component */}
        <Suspense fallback={<div>Loading latest notices...</div>}>
          <LatestNotices />
        </Suspense>
      </div>
      
      {/* Statistics Section - 只在桌面版显示 */}
      <div className="hidden md:block">
        <StatisticsSection />
      </div>
      
      {/* Service Features - 只在桌面版显示 */}
      <div className="hidden md:block">
        <ServiceFeatures />
      </div>
      
      {/* Industry Navigation - Only visible on desktop */}
      <IndustryNavigation />
      
      {/* Mobile Navigation - Only visible on mobile */}
      <MobileNavigation />
    </div>
  );
}
