#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 深色模式类名映射
const darkModeMap = {
  'bg-white': 'bg-white dark:bg-gray-800',
  'bg-gray-50': 'bg-gray-50 dark:bg-gray-700',
  'bg-gray-100': 'bg-gray-100 dark:bg-gray-700',
  'text-gray-900': 'text-gray-900 dark:text-white',
  'text-gray-800': 'text-gray-800 dark:text-gray-200',
  'text-gray-700': 'text-gray-700 dark:text-gray-300',
  'text-gray-600': 'text-gray-600 dark:text-gray-400',
  'text-gray-500': 'text-gray-500 dark:text-gray-400',
  'border-gray-200': 'border-gray-200 dark:border-gray-700',
  'border-gray-300': 'border-gray-300 dark:border-gray-600',
  'hover:bg-gray-50': 'hover:bg-gray-50 dark:hover:bg-gray-700',
  'hover:bg-gray-100': 'hover:bg-gray-100 dark:hover:bg-gray-600',
  'bg-blue-50': 'bg-blue-50 dark:bg-blue-900/20',
  'bg-green-50': 'bg-green-50 dark:bg-green-900/20',
  'bg-yellow-50': 'bg-yellow-50 dark:bg-yellow-900/20',
  'bg-red-50': 'bg-red-50 dark:bg-red-900/20',
  'text-blue-600': 'text-blue-600 dark:text-blue-400',
  'text-green-600': 'text-green-600 dark:text-green-400',
  'text-yellow-600': 'text-yellow-600 dark:text-yellow-400',
  'text-red-600': 'text-red-600 dark:text-red-400',
};

function addDarkMode(content) {
  let updatedContent = content;
  
  // 替换类名
  Object.entries(darkModeMap).forEach(([lightClass, darkClass]) => {
    // 使用正则表达式匹配完整的类名，避免部分匹配
    const regex = new RegExp(`\\b${lightClass}\\b(?!\\s*dark:)`, 'g');
    updatedContent = updatedContent.replace(regex, darkClass);
  });
  
  return updatedContent;
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const updatedContent = addDarkMode(content);
    
    if (content !== updatedContent) {
      fs.writeFileSync(filePath, updatedContent);
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  No changes: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function processDirectory(dirPath) {
  const files = fs.readdirSync(dirPath);
  let updatedCount = 0;
  
  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      updatedCount += processDirectory(fullPath);
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      if (processFile(fullPath)) {
        updatedCount++;
      }
    }
  });
  
  return updatedCount;
}

// 处理 profile 目录下的所有文件
const profileDir = path.join(__dirname, '../app/(main)/profile');

console.log('🌙 Adding dark mode support to profile pages...\n');

const updatedCount = processDirectory(profileDir);

console.log(`\n🎉 Completed! Updated ${updatedCount} files with dark mode support.`);
