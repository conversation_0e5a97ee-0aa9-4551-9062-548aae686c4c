"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import userService from "@/lib/userService";
import { 
  History, 
  Search, 
  Filter,
  Calendar,
  Building,
  Eye,
  Trash2,
  RotateCcw,
  Clock,
  FileText
} from "lucide-react";

interface BrowsingHistory {
  id: number;
  user_id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  publish_date: string;
  site_name: string;
  view_count: number;
  first_viewed_at: string;
  last_viewed_at: string;
  notice_content?: string;
  publish_agency?: string;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export default function HistoryPage() {
  const [history, setHistory] = useState<BrowsingHistory[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: '',
    notice_type: '',
    days: 30
  });

  useEffect(() => {
    loadHistory();
  }, [pagination.page, filters]);

  const loadHistory = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        days: filters.days.toString()
      });

      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.notice_type) {
        params.append('notice_type', filters.notice_type);
      }

      const response = await fetch(`/api/user/history?${params}`, {
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setHistory(data.data.history);
        setPagination(data.data.pagination);
      }
    } catch (error) {
      console.error('Error loading history:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string | number) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const removeHistoryItem = async (historyId: number) => {
    if (!confirm('确定要删除这条浏览记录吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/user/history?id=${historyId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setHistory(prev => prev.filter(item => item.id !== historyId));
        setPagination(prev => ({ ...prev, total: prev.total - 1 }));
      } else {
        alert('删除失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error removing history item:', error);
      alert('删除失败，请重试');
    }
  };

  const clearAllHistory = async () => {
    if (!confirm('确定要清空所有浏览记录吗？此操作不可恢复。')) {
      return;
    }

    try {
      const response = await fetch('/api/user/history?clear_all=true', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setHistory([]);
        setPagination(prev => ({ ...prev, total: 0, totalPages: 0 }));
        alert('浏览记录已清空');
      } else {
        alert('清空失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error clearing history:', error);
      alert('清空失败，请重试');
    }
  };

  const getNoticeTypeColor = (type: string) => {
    switch (type) {
      case '招标公告':
        return 'bg-blue-100 text-blue-800';
      case '中标公告':
        return 'bg-green-100 text-green-800';
      case '变更公告':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}天前`;
    }
  };

  if (isLoading && history.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">浏览记录</h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              查看您的招投标信息浏览历史
            </p>
          </div>
          {history.length > 0 && (
            <button
              onClick={clearAllHistory}
              className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white dark:bg-gray-800 hover:bg-red-50 dark:bg-red-900/20"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              清空记录
            </button>
          )}
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="搜索标题或发布机构..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>

            {/* Type Filter */}
            <select
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={filters.notice_type}
              onChange={(e) => handleFilterChange('notice_type', e.target.value)}
            >
              <option value="">全部类型</option>
              <option value="招标公告">招标公告</option>
              <option value="中标公告">中标公告</option>
              <option value="变更公告">变更公告</option>
            </select>

            {/* Time Range Filter */}
            <select
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={filters.days}
              onChange={(e) => handleFilterChange('days', parseInt(e.target.value))}
            >
              <option value={7}>最近7天</option>
              <option value={30}>最近30天</option>
              <option value={90}>最近90天</option>
              <option value={365}>最近一年</option>
            </select>

            {/* Results Count */}
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <Filter className="w-4 h-4 mr-2" />
              共 {pagination.total} 条记录
            </div>
          </div>
        </div>
      </div>

      {/* History List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        {history.length > 0 ? (
          <>
            <div className="divide-y divide-gray-200">
              {history.map((item) => (
                <div key={item.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getNoticeTypeColor(item.notice_type_desc)}`}>
                          {item.notice_type_desc}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                          <Eye className="w-3 h-3 mr-1" />
                          浏览 {item.view_count} 次
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                          <Clock className="w-3 h-3 mr-1" />
                          {getTimeAgo(item.last_viewed_at)}
                        </span>
                      </div>
                      
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 hover:text-blue-600 dark:text-blue-400 cursor-pointer">
                        {item.notice_title}
                      </h3>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                        <span className="flex items-center">
                          <Building className="w-4 h-4 mr-1" />
                          {item.publish_agency || item.site_name}
                        </span>
                        <span className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(item.publish_date).toLocaleDateString('zh-CN')}
                        </span>
                        <span className="text-xs text-gray-400">
                          首次浏览: {new Date(item.first_viewed_at).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                      
                      {item.notice_content && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                          {item.notice_content.length > 150 
                            ? `${item.notice_content.substring(0, 150)}...` 
                            : item.notice_content
                          }
                        </p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => {
                          // 这里可以跳转到详情页
                          window.open(`/notices/${item.notice_id}`, '_blank');
                        }}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        查看
                      </button>
                      
                      <button
                        onClick={() => removeHistoryItem(item.id)}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200"
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    显示第 {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                    共 {pagination.total} 条记录
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                      className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 disabled:bg-gray-100 dark:bg-gray-700 disabled:text-gray-400"
                    >
                      上一页
                    </button>
                    <span className="px-3 py-1 text-sm text-gray-700 dark:text-gray-300">
                      第 {pagination.page} / {pagination.totalPages} 页
                    </span>
                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                      className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 disabled:bg-gray-100 dark:bg-gray-700 disabled:text-gray-400"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <History className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无浏览记录</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {Object.values(filters).some(v => v && v !== 30) 
                ? '没有找到符合条件的浏览记录，尝试调整筛选条件' 
                : '您还没有浏览任何招投标信息'
              }
            </p>
            {!Object.values(filters).some(v => v && v !== 30) && (
              <div className="mt-6">
                <Link
                  href="/"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  去浏览招投标信息
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
