import { SIT<PERSON>_NAME, host } from "@/data";
import { executeQuery } from "@/lib/db";
import { Metadata, ResolvingMetadata } from "next";
import Link from "next/link";
import { notFound } from "next/navigation";

// Types for bidding data
interface BiddingProject {
  id: number;
  row_guid: string;
  project_name: string;
  tender_project_name: string;
  bidding_contents: string;
  tender_project_type: string;
  bidding_project_content: string;
  tender_mode: string;
  bidding_price: string;
  price_currency: string;
  region_code: string;
  regulatory_authority: string;
  bidding_plan_time: string;
  project_code: string;
  created_at: string;
}

interface BiddingPlan {
  id: number;
  row_guid: string;
  notice_id: string;
  bidding_plan_code: string;
  bidding_plan_name: string;
  bidding_plan_person_liable: string;
  tenderer_name: string;
  submit_time: string;
  create_time: string;
  send_time: string;
  other_content: string;
}

interface TenderFile {
  id: number;
  attach_guid: string;
  row_guid: string;
  file_name: string;
  url: string;
  file_type: string;
  type_name: string;
}

// Function to format date from various formats
function formatDate(dateStr: string | null): string {
  if (!dateStr) return "未知日期";

  // Handle YYYYMMDD format
  if (dateStr.length === 8 && !dateStr.includes("-")) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${year}-${month}-${day}`;
  }

  // Handle ISO date format
  if (dateStr.includes("T")) {
    return dateStr.split("T")[0];
  }

  return dateStr;
}

// Function to get project data by project code
async function getBiddingProjects(
  projectCode: string
): Promise<BiddingProject[]> {
  return await executeQuery<BiddingProject[]>(
    `SELECT * FROM bidding_projects WHERE bidding_plan_code = ?`,
    [projectCode]
  );
}

// Function to get bidding plans by project code
async function getBiddingPlans(rowGuid: string): Promise<BiddingPlan[]> {
  return await executeQuery<BiddingPlan[]>(
    `SELECT * FROM bidding_plans WHERE bidding_plan_code = ?`,
    [rowGuid]
  );
}

// Function to get tender files by row_guid
async function getTenderFiles(rowGuid: string): Promise<TenderFile[]> {
  return await executeQuery<TenderFile[]>(
    `SELECT * FROM tender_files WHERE row_guid = ?`,
    [rowGuid]
  );
}

export const revalidate = 60;
export async function generateMetadata(
  props: { params: Promise<{ projectCode?: string }> },
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { projectCode } = await props.params;
  // const parentData = await parent;

  if (!projectCode) {
    return {};
  }
  const plans = await getBiddingPlans(projectCode);

  const title = SITE_NAME + " | " + plans[0].bidding_plan_name;
  const desc = title;
  const path = `/bidding-detail/${projectCode}`;
  const canonicalPath = path;

  // 图片URL不受语言影响
  const imageUrl = "";

  return {
    metadataBase: new URL(host),
    title: title,
    description: desc,
    alternates: {
      canonical: canonicalPath,
    },
    openGraph: {
      title: title,
      description: desc,
      url: canonicalPath,
      siteName: SITE_NAME,
      images: [{ url: imageUrl }],
    },
  };
}
export default async function BiddingDetailPage({
  params,
}: {
  params: Promise<{ projectCode: string }>;
}) {
  const { projectCode } = await params;

  // First, fetch plans data since it's prioritized
  const plans = await getBiddingPlans(projectCode);

  // If no plans are found, return 404
  if (plans.length === 0) {
    notFound();
  }

  // Then try to get project data, but don't require it
  const projects = await getBiddingProjects(projectCode);

  // Get files for all plans
  let files: TenderFile[] = [];
  if (plans.length > 0) {
    for (const plan of plans) {
      const planFiles = await getTenderFiles(plan.row_guid);
      files = [...files, ...planFiles];
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <Link
          href="/"
          className="text-primary hover:text-primary/80 transition-colors duration-200"
        >
          &larr; 返回首页
        </Link>
      </div>

      {/* Page header with plan name */}
      <div className="bg-card shadow overflow-hidden sm:rounded-lg border border-border mb-8">
        <div className="px-4 py-5 sm:px-6 border-b border-border">
          <h3 className="text-2xl font-semibold leading-6 text-foreground">
            {plans.length > 0 && plans[0].bidding_plan_name
              ? plans[0].bidding_plan_name
              : projects.length > 0 && projects[0].tender_project_name
              ? projects[0].tender_project_name
              : projects.length > 0 && projects[0].project_name
              ? projects[0].project_name
              : "招标计划"}
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-muted-foreground">
            计划编号: {projectCode}
          </p>
        </div>
      </div>

      {/* Plans section - always shown first */}
      <div className="bg-card shadow overflow-hidden sm:rounded-lg border border-border mb-8">
        <div className="px-4 py-5 sm:px-6 border-b border-border">
          <h3 className="text-lg font-medium leading-6 text-foreground">
            招标计划信息
          </h3>
        </div>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                >
                  计划名称
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                >
                  负责人
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                >
                  招标人
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                >
                  提交时间
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {plans.map((plan) => (
                <tr
                  key={plan.id}
                  className="hover:bg-muted/50 transition-colors duration-150"
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {plan.bidding_plan_name || "未命名"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {plan.bidding_plan_person_liable || "未提供"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {plan.tenderer_name || "未提供"}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {formatDate(plan.submit_time) || "未提供"}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Tender files section - now inside the plans section */}
        {files.length > 0 && (
          <div className="border-t border-border">
            <div className="px-4 py-5 sm:px-6 border-b border-border">
              <h4 className="text-md font-medium leading-6 text-foreground">
                招标文件
              </h4>
            </div>

            <ul className="divide-y divide-border">
              {files.map((file) => (
                <li key={file.id} className="px-4 py-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-6 w-6 text-primary"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-foreground truncate">
                        {file.file_name}
                      </p>
                      <p className="text-sm text-muted-foreground truncate">
                        {file.type_name || file.file_type || "未知类型"}
                      </p>
                    </div>
                    <div>
                      {file.url ? (
                        <a
                          href={file.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md bg-primary hover:bg-primary/90 text-primary-foreground dark:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-150"
                        >
                          下载
                        </a>
                      ) : (
                        <span className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md bg-muted text-muted-foreground">
                          无法下载
                        </span>
                      )}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Project details section - display all projects, now at the bottom */}
      {projects.length > 0 && (
        <div className="bg-card shadow overflow-hidden sm:rounded-lg border border-border mb-8">
          <div className="px-4 py-5 sm:px-6 border-b border-border">
            <h3 className="text-lg font-medium leading-6 text-foreground">
              项目详情
            </h3>
          </div>

          {projects.map((project, index) => (
            <div
              key={project.id}
              className={index > 0 ? "mt-8 pt-8 border-t border-border" : ""}
            >
              {projects.length > 1 && (
                <div className="px-4 py-3 bg-muted/20">
                  <h4 className="text-md font-medium text-foreground">
                    项目 {index + 1}:{" "}
                    {project.project_name ||
                      project.tender_project_name ||
                      "未命名项目"}
                  </h4>
                </div>
              )}

              <div className="border-b border-border px-4 py-5 sm:p-0">
                <dl className="sm:divide-y sm:divide-border">
                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      项目名称
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {project.project_name || "未提供"}
                    </dd>
                  </div>

                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      招标项目名称
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {project.tender_project_name || "未提供"}
                    </dd>
                  </div>

                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      招标内容
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {project.bidding_contents || "未提供"}
                    </dd>
                  </div>

                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      招标项目类型
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {project.tender_project_type || "未提供"}
                    </dd>
                  </div>

                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      招标方式
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {project.tender_mode || "未提供"}
                    </dd>
                  </div>

                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      招标价格
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {project.bidding_price
                        ? `${project.bidding_price} ${
                            project.price_currency || ""
                          }`
                        : "未提供"}
                    </dd>
                  </div>

                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      监管机构
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {project.regulatory_authority || "未提供"}
                    </dd>
                  </div>

                  <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                    <dt className="text-sm font-medium text-muted-foreground">
                      招标计划时间
                    </dt>
                    <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
                      {formatDate(project.bidding_plan_time) || "未提供"}
                    </dd>
                  </div>

                  {project.bidding_project_content && (
                    <div className="py-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:py-5 sm:px-6">
                      <dt className="text-sm font-medium text-muted-foreground">
                        项目内容详情
                      </dt>
                      <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0 whitespace-pre-line">
                        {project.bidding_project_content}
                      </dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
