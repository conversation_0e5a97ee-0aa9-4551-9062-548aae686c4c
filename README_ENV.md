# 环境变量配置说明

## 快速开始

1. 在项目根目录创建 `.env.local` 文件
2. 复制以下内容并根据需要修改：

```env
# 后端API基础配置（必填）
NEXT_PUBLIC_API_BASE_URL=http://your-backend-server.com

# 示例：
# NEXT_PUBLIC_API_BASE_URL=http://localhost:8080
# NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com

# 数据库配置 (如果使用数据库)
# DATABASE_URL=mysql://username:password@localhost:3306/database_name

# 其他配置
# SECRET_KEY=your_secret_key_here
```

## 配置说明

### NEXT_PUBLIC_API_BASE_URL

- **说明**: 外部后端 API 服务的基础 URL
- **必填**: 是
- **示例**: `http://localhost:8080` 或 `https://api.yourdomain.com`
- **用途**: 用于调用后端的用户认证、验证码生成和验证等接口

### 用户认证 API

- 用户注册: `${NEXT_PUBLIC_API_BASE_URL}/api/user/register`
- 用户登录: `${NEXT_PUBLIC_API_BASE_URL}/api/user/login`
- 刷新令牌: `${NEXT_PUBLIC_API_BASE_URL}/api/user/refresh`
- 获取用户资料: `${NEXT_PUBLIC_API_BASE_URL}/api/user/profile`

#### 认证机制

- **JWT Token 认证**: 使用 Access Token (30 分钟有效) + Refresh Token (7 天有效)
- **密码要求**: 8-20 位，必须包含字母和数字
- **Token 存储**: 自动存储在 localStorage 中
- **自动刷新**: Token 过期时自动使用 Refresh Token 获取新的 Access Token

#### 注册接口

```
POST ${NEXT_PUBLIC_API_BASE_URL}/api/user/register
Content-Type: application/json

{
  "company_name": "公司名称",      // 必填
  "contact_name": "联系人姓名",    // 必填
  "phone": "13800138000",        // 必填，手机号
  "password": "test123456",      // 必填，8-20位包含字母数字
  "email": "<EMAIL>",   // 可选，邮箱
  "address": "公司地址"           // 可选，地址
}
```

#### 登录接口

```
POST ${NEXT_PUBLIC_API_BASE_URL}/api/user/login
Content-Type: application/json

{
  "phone": "13800138000",
  "password": "test123456"
}
```

**响应格式：**

```javascript
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": { /* 用户信息 */ },
    "access_token": "jwt_token",
    "refresh_token": "refresh_token",
    "token_type": "bearer",
    "expires_in": 1800
  }
}
```

### 验证码功能

- 验证码生成接口: `${NEXT_PUBLIC_API_BASE_URL}/api/captcha`
- 验证码验证接口: `${NEXT_PUBLIC_API_BASE_URL}/api/captcha/verify`
- 支持实时验证（失去焦点时自动验证）
- 调用外部后端 API，不是本项目的 API

#### 验证码生成接口

```
GET ${NEXT_PUBLIC_API_BASE_URL}/api/captcha?timestamp
```

- 返回验证码图片
- 响应头中包含 `captcha-id`，用于后续验证

**实现方式**: 前端通过一次 fetch 请求同时获取：

1. 验证码图片数据（转换为 Blob URL 显示）
2. 响应头中的 `captcha-id`

这样避免了重复请求，提高了性能。

#### 验证码验证接口

```
POST ${NEXT_PUBLIC_API_BASE_URL}/api/captcha/verify
Content-Type: application/json

{
  "captcha-input": "用户输入的验证码",
  "captcha-id": "从获取验证码时header中返回的ID"
}
```

**响应格式：**

```javascript
// 验证成功
{
  "success": true,
  "message": "验证成功"
}

// 验证失败
{
  "success": false,
  "message": "验证失败原因"
}
```

## 注意事项

1. `.env.local` 文件已在 `.gitignore` 中，不会被提交到代码库
2. 环境变量以 `NEXT_PUBLIC_` 开头的可以在客户端访问
3. 修改环境变量后需要重启开发服务器
