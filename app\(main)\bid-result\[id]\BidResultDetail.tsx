"use client";

import { ChevronLeft } from "lucide-react";
import { useState } from "react";
import { File, Award } from "lucide-react";
import Link from "next/link";

// Types for bid result data
export interface BidWinResult {
  id: number;
  row_guid: string;
  notice_id: string;
  tender_project_code?: string;
  tender_project_name?: string;
  tender_code?: string;
  unified_deal_code?: string;
  bid_section_code?: string;
  bid_section_name?: string;
  tender_agency_name?: string;
  tender_agency_code?: string;
  tenderer_name?: string;
  notice_name?: string;
  notice_content?: string;
  notice_send_time?: string;
  notice_media?: string;
  notice_nature?: string;
  notice_code?: string;
  bulletin_type?: string;
  bulletin_duty?: string;
  win_bidder_name?: string;
  win_bidder_code?: string;
  bid_amount?: number;
  bid_currency?: string;
  win_price?: string;
  currency_code?: string;
  price_unit?: string;
  other_bid_price?: string;
  other_win_price?: string;
  price_from_name?: string;
  rate?: string;
  bid_win_notice_issue_time?: string;
  time_limit?: number;
  bid_manager?: string;
  bidder_code?: string;
  bid_win_notice_num?: string;
  union_name?: string;
  union_code?: string;
  region_code?: string;
  announcement_connect?: string;
  other_content?: string;
  url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface TenderFile {
  id: number;
  attach_guid: string;
  row_guid: string;
  file_name: string;
  url: string;
  file_type: string;
  type_name: string;
}

// Client component for bid result detail
export default function BidResultDetail({
  bidResult,
  files,
  formattedNoticeTime,
  formattedWinTime,
}: {
  bidResult: BidWinResult;
  files: TenderFile[];
  formattedNoticeTime: string;
  formattedWinTime: string;
}) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Desktop Header */}
      <div className="hidden md:block bg-white dark:bg-gray-800 border-b">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            中标结果详情
          </h1>
        </div>
      </div>

      {/* Mobile Header */}
      <div className="md:hidden bg-green-500 text-white p-4 flex items-center">
        <Link href="/notices/1" className="mr-2">
          <ChevronLeft className="h-6 w-6" />
        </Link>
        <h1 className="text-xl font-medium text-center flex-1">中标结果详情</h1>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          {/* 详情页标题部分 */}
          <div className="px-4 py-6 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-start">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {bidResult.notice_name || bidResult.tender_project_name}
              </h2>
              <div className="hidden md:flex space-x-2">
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
                    />
                  </svg>
                  打印
                </button>
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                    />
                  </svg>
                  收藏
                </button>
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                    />
                  </svg>
                  导出PDF
                </button>
              </div>
            </div>
            <div className="mt-2 flex flex-wrap items-center text-sm text-gray-500 dark:text-gray-400">
              {formattedWinTime && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">发布时间：</span>
                  {formattedWinTime}
                </div>
              )}
              {bidResult.tender_project_code && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">项目编号：</span>
                  {bidResult.tender_project_code}
                </div>
              )}
              <div className="mr-6 mb-2">
                <span className="font-medium">公告类型：</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  中标结果
                </span>
              </div>
              {bidResult.tenderer_name && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">招标人：</span>
                  {bidResult.tenderer_name}
                </div>
              )}
            </div>
          </div>

          {/* 详情页内容部分 */}
          <div className="px-4 py-6 sm:px-6">
            {/* 内容区域 - 左右两栏布局 */}
            <div className="py-6 grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* 左侧内容区域 */}
              <div className="md:col-span-3 prose max-w-none dark:prose-invert">
                {/* 中标公告内容 */}
                {bidResult.notice_content && (
                  <div className="mb-8">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      中标公告内容
                    </h3>
                    <div
                      className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap"
                      dangerouslySetInnerHTML={{
                        __html: bidResult.notice_content,
                      }}
                    />
                  </div>
                )}

                {/* 中标结果 */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <Award className="h-5 w-5 mr-2 text-green-600" />
                    中标结果
                  </h3>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                        {bidResult.tender_project_name && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                              项目名称
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              {bidResult.tender_project_name}
                            </td>
                          </tr>
                        )}
                        {bidResult.bid_section_name && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              标段名称
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              {bidResult.bid_section_name}
                            </td>
                          </tr>
                        )}
                        <tr>
                          <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                            招标人
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                            {bidResult.tenderer_name || "-"}
                          </td>
                          <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                            招标代理
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                            {bidResult.tender_agency_name || "-"}
                          </td>
                        </tr>
                        <tr className="bg-green-50 dark:bg-green-900">
                          <td className="px-4 py-3 bg-green-100 dark:bg-green-800 text-sm font-medium text-green-700 dark:text-green-300">
                            中标人
                          </td>
                          <td className="px-4 py-3 text-sm font-bold text-green-900 dark:text-green-100">
                            {bidResult.win_bidder_name || "-"}
                          </td>
                          <td className="px-4 py-3 bg-green-100 dark:bg-green-800 text-sm font-medium text-green-700 dark:text-green-300">
                            中标价格
                          </td>
                          <td className="px-4 py-3 text-sm font-bold text-green-900 dark:text-green-100">
                            {bidResult.bid_amount
                              ? `${bidResult.bid_amount} ${
                                  bidResult.bid_currency ||
                                  bidResult.price_unit ||
                                  "元"
                                }`
                              : bidResult.win_price || "-"}
                          </td>
                        </tr>
                        {bidResult.bid_manager && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              项目经理
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.bid_manager}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              工期
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.time_limit
                                ? `${bidResult.time_limit}天`
                                : "-"}
                            </td>
                          </tr>
                        )}
                        {bidResult.union_name && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              联合体名称
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              {bidResult.union_name}
                            </td>
                          </tr>
                        )}
                        {bidResult.rate && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              下浮率
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              {bidResult.rate}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 项目基本信息 */}
                <div className="mt-6 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    项目基本信息
                  </h3>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                        {bidResult.tender_code && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                              招标编号
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.tender_code}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                              统一交易编号
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.unified_deal_code || "-"}
                            </td>
                          </tr>
                        )}
                        {bidResult.bid_section_code && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              标段编号
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.bid_section_code}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              中标通知书编号
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.bid_win_notice_num || "-"}
                            </td>
                          </tr>
                        )}
                        {bidResult.region_code && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              地区代码
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.region_code}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              公告职责
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidResult.bulletin_duty || "-"}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 价格信息 */}
                {(bidResult.other_bid_price ||
                  bidResult.other_win_price ||
                  bidResult.price_from_name) && (
                  <div className="mt-6 border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      价格信息详情
                    </h3>
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg space-y-3">
                      {bidResult.other_bid_price && (
                        <div>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            其他投标价格：
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                            {bidResult.other_bid_price}
                          </span>
                        </div>
                      )}
                      {bidResult.other_win_price && (
                        <div>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            其他中标价格：
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                            {bidResult.other_win_price}
                          </span>
                        </div>
                      )}
                      {bidResult.price_from_name && (
                        <div>
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            价格来源：
                          </span>
                          <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                            {bidResult.price_from_name}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 附件 */}
                <div className="mt-6 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    相关附件
                  </h3>
                  {files.length > 0 ? (
                    <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                      {files.map((file, index) => (
                        <li key={index} className="py-4">
                          <div className="flex items-center space-x-4">
                            <div className="flex-shrink-0">
                              <File className="h-6 w-6 text-gray-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {file.file_name}
                                {file.file_type}
                              </p>
                            </div>
                            <div>
                              <a
                                href={file.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                              >
                                下载
                              </a>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">暂无附件</p>
                  )}
                </div>

                {/* 其他内容 */}
                {bidResult.other_content && (
                  <div className="mt-6 border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      其他内容
                    </h3>
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {bidResult.other_content}
                      </p>
                    </div>
                  </div>
                )}

                {/* 基本信息 */}
                <div className="mt-8 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    基本信息
                  </h3>
                  <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                    {bidResult.notice_media && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          来源媒体
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {bidResult.notice_media}
                        </dd>
                      </div>
                    )}
                    {bidResult.bulletin_type && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          公告类型
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {bidResult.bulletin_type}
                        </dd>
                      </div>
                    )}
                    {bidResult.announcement_connect && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          公告联系
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {bidResult.announcement_connect}
                        </dd>
                      </div>
                    )}
                    {formattedNoticeTime && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          公告发送时间
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {formattedNoticeTime}
                        </dd>
                      </div>
                    )}
                  </dl>
                </div>

                {/* 相关链接 */}
                <div className="mt-6 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    相关推荐
                  </h3>
                  <ul className="space-y-2">
                    <li>
                      <Link
                        href="/notices/1?noticeType=中标结果"
                        className="text-green-600 hover:underline dark:text-green-400"
                      >
                        查看更多中标结果
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/notices/1"
                        className="text-green-600 hover:underline dark:text-green-400"
                      >
                        返回公告列表
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>

              {/* 右侧区域 - 招标热词 */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 sticky top-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    中标热词
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer">
                      建筑工程
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer">
                      市政工程
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-purple-100 text-purple-800 hover:bg-purple-200 cursor-pointer">
                      设计方案
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-yellow-100 text-yellow-800 hover:bg-yellow-200 cursor-pointer">
                      项目咨询
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-red-100 text-red-800 hover:bg-red-200 cursor-pointer">
                      工程项目
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800 hover:bg-indigo-200 cursor-pointer">
                      中标公告
                    </span>
                  </div>

                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mt-6 mb-3">
                    热门中标
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-green-600 hover:underline dark:text-green-400"
                      >
                        广州市车辆绿化工程中标结果公告
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-green-600 hover:underline dark:text-green-400"
                      >
                        2025年江苏省交通设施维护工程中标
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-green-600 hover:underline dark:text-green-400"
                      >
                        北京市公园绿化管理服务项目中标
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
