"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import userService from "@/lib/userService";
import { 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Search,
  Filter,
  BookmarkCheck,
  Bell,
  Mail,
  Smartphone
} from "lucide-react";

interface Subscription {
  id: number;
  name: string;
  keywords: string[];
  regions: string[];
  categories: string[];
  subscription_type: 'tender_announcement' | 'winning_announcement' | 'both';
  min_amount?: number;
  max_amount?: number;
  wechat_push: boolean;
  email_push: boolean;
  push_frequency: 'realtime' | 'daily' | 'custom';
  push_times?: string[];
  daily_push_enabled: boolean;
  realtime_push_enabled: boolean;
  is_active: boolean;
  match_count: number;
  last_matched: string | null;
  created_at: string;
  updated_at: string;
}

export default function SubscriptionsPage() {
  const router = useRouter();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [filteredSubscriptions, setFilteredSubscriptions] = useState<Subscription[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterType, setFilterType] = useState<string>("all");
  const [filterStatus, setFilterStatus] = useState<string>("all");

  useEffect(() => {
    if (!userService.isLoggedIn()) {
      router.push("/login");
      return;
    }
    loadSubscriptions();
  }, []);

  useEffect(() => {
    filterSubscriptions();
  }, [subscriptions, searchTerm, filterType, filterStatus]);

  const loadSubscriptions = async () => {
    try {
      const response = await fetch('/api/subscriptions', {
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setSubscriptions(data.data);
      } else {
        console.error('Failed to load subscriptions:', data.error);
      }
    } catch (error) {
      console.error('Error loading subscriptions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterSubscriptions = () => {
    let filtered = subscriptions;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(sub => 
        sub.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sub.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Type filter
    if (filterType !== "all") {
      filtered = filtered.filter(sub => sub.subscription_type === filterType);
    }

    // Status filter
    if (filterStatus !== "all") {
      filtered = filtered.filter(sub => 
        filterStatus === "active" ? sub.is_active : !sub.is_active
      );
    }

    setFilteredSubscriptions(filtered);
  };

  const toggleSubscriptionStatus = async (id: number, currentStatus: boolean) => {
    try {
      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          id,
          is_active: !currentStatus
        })
      });

      const data = await response.json();
      if (data.success) {
        setSubscriptions(prev => 
          prev.map(sub => 
            sub.id === id ? { ...sub, is_active: !currentStatus } : sub
          )
        );
      } else {
        alert('操作失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error toggling subscription status:', error);
      alert('操作失败，请重试');
    }
  };

  const deleteSubscription = async (id: number) => {
    if (!confirm('确定要删除这个订阅吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/subscriptions?id=${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setSubscriptions(prev => prev.filter(sub => sub.id !== id));
      } else {
        alert('删除失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error deleting subscription:', error);
      alert('删除失败，请重试');
    }
  };

  const getSubscriptionTypeText = (type: string) => {
    switch (type) {
      case 'tender_announcement':
        return '招标公告';
      case 'winning_announcement':
        return '中标公告';
      case 'both':
        return '全部';
      default:
        return type;
    }
  };

  const getPushMethodIcons = (subscription: Subscription) => {
    const icons = [];
    if (subscription.wechat_push) {
      icons.push(<Bell key="wechat" className="w-4 h-4 text-green-600 dark:text-green-400" />);
    }
    if (subscription.email_push) {
      icons.push(<Mail key="email" className="w-4 h-4 text-blue-600 dark:text-blue-400" />);
    }
    return icons;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-700 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">我的订阅</h1>
              <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
                管理您的订阅设置，接收相关招投标信息推送
              </p>
            </div>
            <Link
              href="/profile/subscriptions/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
            >
              <Plus className="w-4 h-4 mr-2" />
              新建订阅
            </Link>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="搜索订阅名称或关键词..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Type Filter */}
              <select
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
              >
                <option value="all">全部类型</option>
                <option value="tender_announcement">招标公告</option>
                <option value="winning_announcement">中标公告</option>
                <option value="both">全部</option>
              </select>

              {/* Status Filter */}
              <select
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <option value="all">全部状态</option>
                <option value="active">活跃</option>
                <option value="inactive">暂停</option>
              </select>

              {/* Results Count */}
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Filter className="w-4 h-4 mr-2" />
                共 {filteredSubscriptions.length} 个订阅
              </div>
            </div>
          </div>
        </div>

        {/* Subscriptions List */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          {filteredSubscriptions.length > 0 ? (
            <div className="divide-y divide-gray-200">
              {filteredSubscriptions.map((subscription) => (
                <div key={subscription.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                          {subscription.name}
                        </h3>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          subscription.is_active 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                        }`}>
                          {subscription.is_active ? '活跃' : '暂停'}
                        </span>
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {getSubscriptionTypeText(subscription.subscription_type)}
                        </span>
                      </div>
                      
                      <div className="mt-2 space-y-1">
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">关键词:</span> {subscription.keywords.join(', ')}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          <span className="font-medium">地区:</span> {subscription.regions.join(', ')}
                        </p>
                        {subscription.categories.length > 0 && (
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">分类:</span> {subscription.categories.join(', ')}
                          </p>
                        )}
                        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                          <span>匹配数: {subscription.match_count}</span>
                          <span>创建时间: {new Date(subscription.created_at).toLocaleDateString('zh-CN')}</span>
                          <div className="flex items-center space-x-1">
                            <span>推送方式:</span>
                            {getPushMethodIcons(subscription)}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => toggleSubscriptionStatus(subscription.id, subscription.is_active)}
                        className={`inline-flex items-center px-3 py-1 rounded-md text-sm font-medium ${
                          subscription.is_active
                            ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200'
                            : 'text-green-700 bg-green-100 hover:bg-green-200'
                        }`}
                      >
                        {subscription.is_active ? (
                          <>
                            <Pause className="w-4 h-4 mr-1" />
                            暂停
                          </>
                        ) : (
                          <>
                            <Play className="w-4 h-4 mr-1" />
                            启用
                          </>
                        )}
                      </button>
                      
                      <Link
                        href={`/profile/subscriptions/${subscription.id}/edit`}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200"
                      >
                        <Edit className="w-4 h-4 mr-1" />
                        编辑
                      </Link>
                      
                      <button
                        onClick={() => deleteSubscription(subscription.id)}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200"
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <BookmarkCheck className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                {subscriptions.length === 0 ? '暂无订阅' : '没有找到匹配的订阅'}
              </h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {subscriptions.length === 0 
                  ? '创建您的第一个订阅来接收推送通知' 
                  : '尝试调整搜索条件或筛选器'
                }
              </p>
              {subscriptions.length === 0 && (
                <div className="mt-6">
                  <Link
                    href="/profile/subscriptions/new"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    创建订阅
                  </Link>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
