import mysql from "mysql2/promise";

// MySQL connection configuration for bid_mysql server
const dbConfig = {
  host: process.env.DB_HOST || "localhost",
  user: process.env.DB_USER || "root",
  password: process.env.DB_PASSWORD || "",
  database: process.env.DB_NAME || "bid_mysql",
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
};

// Create a connection pool
const pool = mysql.createPool(dbConfig);

export async function executeQuery<T>(
  query: string,
  params: any[] = []
): Promise<T> {
  // console.log(query, params);
  try {
    // Check if the query contains LIMIT or OFFSET keywords
    // If it does, use query() instead of execute() since mysql2 doesn't support
    // parameterized LIMIT/OFFSET
    if (/\bLIMIT\b|\bOFFSET\b/i.test(query) && params.length === 0) {
      const [rows] = await pool.query(query);
      return rows as T;
    } else {
      const [rows] = await pool.execute(query, params);
      return rows as T;
    }
  } catch (error: any) {
    console.error("Database query error:", error);
    throw new Error(`Database query failed: ${error.message}`);
  }
}

export default { executeQuery };
