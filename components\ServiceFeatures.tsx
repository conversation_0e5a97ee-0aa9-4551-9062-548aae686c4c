"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { Newspaper, SearchCheck, Clock, FileCheck, BarChart, Database, Target, ShieldCheck } from "lucide-react";

interface ServiceFeature {
  title: string;
  icon: React.ReactNode;
  description: string;
  detailTitle: string;
  detailDescription: string;
  detailImageSrc: string;
}

const features: ServiceFeature[] = [
  {
    title: "海量数据",
    icon: <Database className="h-8 w-8 md:h-12 md:w-12 text-blue-500" />,
    description: "覆盖全国各地招标信息",
    detailTitle: "数据助力市场分析，获得精准商机",
    detailDescription: "100+个行业数据分析，为您提供第一手资料，覆盖全国31个省市自治区的招标信息",
    detailImageSrc: "/images/data-analysis.png"
  },
  {
    title: "精准匹配",
    icon: <Target className="h-8 w-8 md:h-12 md:w-12 text-blue-500" />,
    description: "智能推荐相关项目",
    detailTitle: "智能匹配您的业务需求，精准推送商机",
    detailDescription: "基于大数据智能算法，为您精准筛选匹配度高的投标项目，提高中标率",
    detailImageSrc: "/images/smart-matching.png"
  },
  {
    title: "实时更新",
    icon: <Clock className="h-8 w-8 md:h-12 md:w-12 text-blue-500" />,
    description: "第一时间获取最新招标",
    detailTitle: "7×24小时实时监控，投标先机尽在掌握",
    detailDescription: "系统每日实时更新千余条招标信息，确保您第一时间获取最新商机",
    detailImageSrc: "/images/realtime-update.png"
  },
  {
    title: "专业服务",
    icon: <ShieldCheck className="h-8 w-8 md:h-12 md:w-12 text-blue-500" />,
    description: "提供全流程投标支持",
    detailTitle: "专业团队全程服务，助您轻松应对投标挑战",
    detailDescription: "提供招标文件分析、标书制作、投标培训等全流程服务，让投标更加高效",
    detailImageSrc: "/images/pro-service.png"
  }
];

export default function ServiceFeatures() {
  const [activeFeature, setActiveFeature] = useState<ServiceFeature>(features[0]);

  return (
    <div className="py-10 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-center text-xl font-semibold text-gray-900 dark:text-gray-100 mb-12">招标通-招投标服务平台</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {features.map((feature) => (
            <div 
              key={feature.title} 
              className={`bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm text-center cursor-pointer transition-all ${activeFeature.title === feature.title ? 'ring-2 ring-blue-500 dark:ring-blue-400' : 'hover:shadow-md'}`}
              onMouseEnter={() => setActiveFeature(feature)}
              onClick={() => setActiveFeature(feature)}
            >
              <div className="flex justify-center mb-4">
                {feature.icon}
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">{feature.title}</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">{feature.description}</p>
            </div>
          ))}
        </div>
        
        <div className="mt-12 w-full">
          <div className="w-full">
            <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-lg p-8 flex flex-col md:flex-row items-center w-full">
              <div className="md:w-2/3 mb-6 md:mb-0 md:mr-8">
                <h3 className="text-xl font-bold text-white mb-2">{activeFeature.detailTitle}</h3>
                <p className="text-blue-100 text-sm mb-4">{activeFeature.detailDescription}</p>
                <Link href="/register" className="inline-block bg-white dark:bg-gray-700 text-blue-600 dark:text-blue-300 px-4 py-2 rounded-md font-medium text-sm hover:bg-blue-50 dark:hover:bg-gray-600 transition-colors">
                  免费注册
                </Link>
              </div>
              <div className="md:w-1/3 flex justify-center">
                <Image 
                  src={activeFeature.detailImageSrc} 
                  width={240} 
                  height={180} 
                  alt={activeFeature.title}
                  className="object-contain"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
