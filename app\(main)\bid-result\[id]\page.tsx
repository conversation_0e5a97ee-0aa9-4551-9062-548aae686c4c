import { notFound } from "next/navigation";
import BidResultDetail from "./BidResultDetail";
import type { BidWinResult, TenderFile } from "./BidResultDetail";
import { executeQuery } from "@/lib/db";
import { NoticeDetail } from "@/lib/type";

// 格式化日期时间
function formatDateTime(dateTimeString: string): string {
  if (!dateTimeString) return "";

  // 处理不同的日期时间格式
  try {
    let date: Date;

    // 如果是 YYYYMMDD 格式
    if (dateTimeString.length === 8 && /^\d{8}$/.test(dateTimeString)) {
      const year = dateTimeString.substring(0, 4);
      const month = dateTimeString.substring(4, 6);
      const day = dateTimeString.substring(6, 8);
      date = new Date(`${year}-${month}-${day}`);
      return `${year}-${month}-${day}`;
    }

    // 如果是 YYYYMMDDHHMMSS 格式
    if (dateTimeString.length === 14 && /^\d{14}$/.test(dateTimeString)) {
      const year = dateTimeString.substring(0, 4);
      const month = dateTimeString.substring(4, 6);
      const day = dateTimeString.substring(6, 8);
      const hour = dateTimeString.substring(8, 10);
      const minute = dateTimeString.substring(10, 12);
      const second = dateTimeString.substring(12, 14);
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }

    // 尝试解析为标准日期格式
    date = new Date(dateTimeString);

    if (isNaN(date.getTime())) {
      return dateTimeString; // 如果解析失败，返回原始字符串
    }

    // 返回格式化的日期时间
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  } catch (error) {
    return dateTimeString;
  }
}

// Function to get notice data by ID
async function getNoticeDetail(id: string): Promise<NoticeDetail | null> {
  try {
    const results = await executeQuery<NoticeDetail[]>(
      `SELECT * FROM trading_notices WHERE id = ?`,
      [id]
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error("Error fetching notice detail:", error);
    return null;
  }
}

// 获取中标结果详情
async function getBidWinResult(id: string): Promise<BidWinResult | null> {
  try {
    const results = await executeQuery<BidWinResult[]>(
      "SELECT * FROM bid_win_results WHERE notice_id = ?",
      [id]
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error("Error fetching bid win result:", error);
    return null;
  }
}

// 获取相关文件
async function getBidWinResultFiles(rowGuid: string): Promise<TenderFile[]> {
  try {
    const results = await executeQuery<TenderFile[]>(
      "SELECT * FROM tender_files WHERE row_guid = ?",
      [rowGuid]
    );

    return results;
  } catch (error) {
    console.error("Error fetching bid win result files:", error);
    return [];
  }
}

export default async function BidResultPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const notice = await getNoticeDetail(id);
  const bidResult = await getBidWinResult(notice?.notice_id || "");

  if (!bidResult) {
    notFound();
  }

  // 获取相关文件
  const files = await getBidWinResultFiles(bidResult.row_guid);

  // 格式化时间
  const formattedNoticeTime = formatDateTime(bidResult.notice_send_time || "");
  const formattedWinTime = formatDateTime(
    bidResult.bid_win_notice_issue_time || ""
  );

  return (
    <BidResultDetail
      bidResult={bidResult}
      files={files}
      formattedNoticeTime={formattedNoticeTime}
      formattedWinTime={formattedWinTime}
    />
  );
}

// 生成静态参数（可选）
export async function generateStaticParams() {
  // 这里可以预生成一些常用的页面参数
  return [];
}

// 页面元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const notice = await getNoticeDetail(id);
  const bidResult = await getBidWinResult(notice?.notice_id || "");

  if (!bidResult) {
    return {
      title: "中标结果详情",
    };
  }

  return {
    title: `${
      bidResult.notice_name || bidResult.tender_project_name || "中标结果详情"
    }`,
    description: `中标结果详情 - 中标人: ${
      bidResult.win_bidder_name || ""
    } - 招标人: ${bidResult.tenderer_name || ""}`,
  };
}
