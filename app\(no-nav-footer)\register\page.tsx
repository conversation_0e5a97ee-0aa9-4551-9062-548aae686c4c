"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import userService, { type RegisterData } from "@/lib/userService";

export default function RegisterPage() {
  const router = useRouter();
  const captchaUrlDefault = process.env.NEXT_PUBLIC_API_BASE_URL
    ? `${
        process.env.NEXT_PUBLIC_API_BASE_URL
      }/api/captcha?${new Date().getTime()}`
    : "/api/captcha?" + new Date().getTime();
  const [formData, setFormData] = useState({
    companyName: "",
    contactName: "",
    phone: "",
    password: "",
    confirmPassword: "",
    email: "",
    address: "",
    captcha: "",
    agreeTerms: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [captchaUrl, setCaptchaUrl] = useState(captchaUrlDefault);
  const [captchaStatus, setCaptchaStatus] = useState<
    "idle" | "verifying" | "success" | "error"
  >("idle");
  const [captchaMessage, setCaptchaMessage] = useState("");
  const [captchaId, setCaptchaId] = useState("");

  // 生成随机图形验证码URL (模拟)
  useEffect(() => {
    generateCaptcha();

    // 清理函数：组件卸载时清理Blob URL
    return () => {
      if (captchaUrl && captchaUrl.startsWith("blob:")) {
        URL.revokeObjectURL(captchaUrl);
      }
    };
  }, []);

  // 当captchaUrl变化时，清理旧的Blob URL
  useEffect(() => {
    return () => {
      if (captchaUrl && captchaUrl.startsWith("blob:")) {
        URL.revokeObjectURL(captchaUrl);
      }
    };
  }, [captchaUrl]);

  const generateCaptcha = async () => {
    const timestamp = new Date().getTime();
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;

    let captchaApiUrl;
    if (baseUrl) {
      captchaApiUrl = `${baseUrl}/api/captcha?${timestamp}`;
    } else {
      // 如果没有配置外部API，使用本地API作为回退
      captchaApiUrl = `/api/captcha?${timestamp}`;
    }

    // 重置验证码状态
    setCaptchaStatus("idle");
    setCaptchaMessage("");
    setFormData((prev) => ({ ...prev, captcha: "" }));
    setCaptchaId("");

    try {
      // 获取验证码图片和header中的captcha-id
      const response = await fetch(captchaApiUrl);

      if (response.ok) {
        // 从响应头中获取captcha-id
        const captchaIdFromHeader = response.headers.get("captcha-id");
        if (captchaIdFromHeader) {
          setCaptchaId(captchaIdFromHeader);
        }

        // 清理之前的Blob URL（如果存在）
        if (captchaUrl && captchaUrl.startsWith("blob:")) {
          URL.revokeObjectURL(captchaUrl);
        }

        // 将图片数据转换为Blob URL
        const imageBlob = await response.blob();
        const imageUrl = URL.createObjectURL(imageBlob);

        // 设置图片URL
        setCaptchaUrl(imageUrl);
      } else {
        console.error("获取验证码失败:", response.statusText);
        // 如果获取失败，回退到直接使用URL
        setCaptchaUrl(captchaApiUrl);
      }
    } catch (error) {
      console.error("获取验证码失败:", error);
      // 发生错误时回退到直接使用URL
      setCaptchaUrl(captchaApiUrl);
    }
  };

  const verifyCaptcha = async (captchaValue: string) => {
    if (!captchaValue.trim()) {
      setCaptchaStatus("idle");
      setCaptchaMessage("");
      return;
    }

    if (!captchaId) {
      setCaptchaStatus("error");
      setCaptchaMessage("验证码ID获取失败，请刷新验证码");
      return;
    }

    setCaptchaStatus("verifying");
    setCaptchaMessage("验证中...");

    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
      if (!baseUrl) {
        throw new Error("API基础URL未配置");
      }

      const response = await fetch(`${baseUrl}/api/captcha/verify`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          captcha_input: captchaValue,
          captcha_id: captchaId,
          // 可以添加其他验证需要的参数
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setCaptchaStatus("success");
        setCaptchaMessage("验证成功");
      } else {
        setCaptchaStatus("error");
        setCaptchaMessage(result.message || "验证失败，请重新输入");
      }
    } catch (error) {
      console.error("验证码验证失败:", error);
      setCaptchaStatus("error");
      setCaptchaMessage("验证失败，请检查网络连接");
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.agreeTerms) {
      alert("请先同意用户协议");
      return;
    }

    // 密码确认验证
    if (formData.password !== formData.confirmPassword) {
      alert("两次输入的密码不一致");
      return;
    }

    // 密码强度验证
    if (formData.password.length < 8 || formData.password.length > 20) {
      alert("密码长度必须为8-20位");
      return;
    }

    if (!/^(?=.*[A-Za-z])(?=.*\d)/.test(formData.password)) {
      alert("密码必须包含字母和数字");
      return;
    }

    if (captchaStatus !== "success") {
      alert("请先通过图形验证码验证");
      return;
    }

    setIsLoading(true);
    try {
      // 构建注册数据
      const registerData: RegisterData = {
        company_name: formData.companyName,
        contact_name: formData.contactName,
        phone: formData.phone,
        password: formData.password,
      };

      // 添加可选字段
      if (formData.email.trim()) {
        registerData.email = formData.email;
      }
      if (formData.address.trim()) {
        registerData.address = formData.address;
      }

      // 调用注册API
      const result = await userService.register(registerData);

      if (result.success) {
        alert("注册成功！请使用手机号和密码登录");
        router.push("/login");
      } else {
        alert(result.message || "注册失败");
      }
    } catch (error: any) {
      console.error("注册失败:", error);
      alert(error.message || "注册失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // 如果是验证码输入框，重置验证状态
    if (name === "captcha") {
      setCaptchaStatus("idle");
      setCaptchaMessage("");
    }
  };

  const handleCaptchaBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const captchaValue = e.target.value;
    verifyCaptcha(captchaValue);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-green-50 to-blue-100 dark:from-gray-900 dark:to-gray-800 py-12 px-4 sm:px-6 lg:px-8">
      {/* Logo 区域 */}
      <div className="mb-8 flex items-center space-x-3">
        <img src="/logo.svg" alt="Logo" className="w-10 h-10" />
        <h1 className="text-2xl font-bold text-gray-800 dark:text-white">
          招标通
        </h1>
      </div>

      <div className="max-w-5xl w-full bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
        <div className="flex">
          {/* 左侧宣传区域 */}
          <div className="hidden lg:flex lg:w-2/5 bg-gradient-to-br from-green-600 to-blue-700 relative p-12">
            <div className="absolute inset-0 bg-black opacity-10"></div>
            <div className="relative z-10 flex flex-col justify-center text-white">
              <div className="mb-8">
                <h1 className="text-3xl font-bold mb-3">加入我们</h1>
                <p className="text-lg text-green-100">
                  注册账户，开启您的招投标信息服务
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <svg
                    className="w-6 h-6"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium">连接更多商业机会</p>
                  <p className="text-sm text-green-200">专业的招投标信息平台</p>
                </div>
              </div>
            </div>
          </div>

          {/* 右侧注册表单 */}
          <div className="w-full lg:w-3/5 p-8">
            <div className="max-w-md mx-auto">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  创建账户
                </h2>
                <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                  填写信息完成注册
                </p>
              </div>

              <form className="space-y-4" onSubmit={handleSubmit}>
                <div>
                  <label
                    htmlFor="companyName"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    公司名称 *
                  </label>
                  <input
                    id="companyName"
                    name="companyName"
                    type="text"
                    required
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                    placeholder="请输入公司名称"
                    value={formData.companyName}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label
                    htmlFor="contactName"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    联系人姓名 *
                  </label>
                  <input
                    id="contactName"
                    name="contactName"
                    type="text"
                    required
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                    placeholder="请输入联系人姓名"
                    value={formData.contactName}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label
                    htmlFor="phone"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    手机号码 *
                  </label>
                  <input
                    id="phone"
                    name="phone"
                    type="tel"
                    required
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                    placeholder="请输入手机号码"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label
                    htmlFor="email"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    邮箱地址
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                    placeholder="请输入邮箱地址（可选）"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label
                    htmlFor="address"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    公司地址
                  </label>
                  <input
                    id="address"
                    name="address"
                    type="text"
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                    placeholder="请输入公司地址（可选）"
                    value={formData.address}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label
                    htmlFor="password"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    登录密码 *
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                    placeholder="8-20位，包含字母和数字"
                    value={formData.password}
                    onChange={handleInputChange}
                  />
                </div>

                <div>
                  <label
                    htmlFor="confirmPassword"
                    className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                  >
                    确认密码 *
                  </label>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    required
                    className="w-full px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors text-sm"
                    placeholder="请再次输入密码"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="flex space-x-3">
                  <div className="flex-1">
                    <label
                      htmlFor="captcha"
                      className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
                    >
                      图形验证码 *
                    </label>
                    <div className="relative">
                      <input
                        id="captcha"
                        name="captcha"
                        type="text"
                        required
                        className={`w-full px-3 py-2.5 border rounded-lg placeholder-gray-400 text-gray-900 dark:text-white dark:bg-gray-700 focus:outline-none focus:ring-2 focus:border-transparent transition-colors text-sm pr-8 ${
                          captchaStatus === "success"
                            ? "border-green-500 focus:ring-green-500"
                            : captchaStatus === "error"
                            ? "border-red-500 focus:ring-red-500"
                            : "border-gray-300 dark:border-gray-600 focus:ring-blue-500"
                        }`}
                        placeholder="验证码"
                        value={formData.captcha}
                        onChange={handleInputChange}
                        onBlur={handleCaptchaBlur}
                        title={captchaMessage || ""}
                      />
                      {/* 验证状态图标和消息提示 */}
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        {captchaStatus === "verifying" && (
                          <div className="flex items-center space-x-1">
                            <svg
                              className="animate-spin h-4 w-4 text-blue-500"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                            >
                              <circle
                                className="opacity-25"
                                cx="12"
                                cy="12"
                                r="10"
                                stroke="currentColor"
                                strokeWidth="4"
                              ></circle>
                              <path
                                className="opacity-75"
                                fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                              ></path>
                            </svg>
                            <span className="text-xs text-blue-600">
                              验证中
                            </span>
                          </div>
                        )}
                        {captchaStatus === "success" && (
                          <div className="flex items-center space-x-1">
                            <svg
                              className="h-4 w-4 text-green-500"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span className="text-xs text-green-600">
                              验证成功
                            </span>
                          </div>
                        )}
                        {captchaStatus === "error" && (
                          <div className="flex items-center space-x-1">
                            <svg
                              className="h-4 w-4 text-red-500"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span className="text-xs text-red-600">
                              验证失败
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col justify-end">
                    <img
                      src={captchaUrl}
                      alt="验证码"
                      className="h-10 w-28 border border-gray-300 dark:border-gray-600 rounded cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={generateCaptcha}
                      title="点击刷新验证码"
                    />
                  </div>
                </div>

                <div className="flex items-start space-x-2 pt-2">
                  <input
                    id="agreeTerms"
                    name="agreeTerms"
                    type="checkbox"
                    required
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
                    checked={formData.agreeTerms}
                    onChange={handleInputChange}
                  />
                  <label
                    htmlFor="agreeTerms"
                    className="text-sm text-gray-900 dark:text-gray-300 leading-tight"
                  >
                    我已阅读并同意{" "}
                    <Link
                      href="/terms"
                      className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
                    >
                      用户协议
                    </Link>{" "}
                    和{" "}
                    <Link
                      href="/privacy"
                      className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
                    >
                      隐私政策
                    </Link>
                  </label>
                </div>

                <div className="pt-2">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {isLoading ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        注册中...
                      </>
                    ) : (
                      "注册"
                    )}
                  </button>
                </div>

                <div className="text-center pt-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    已有账户？{" "}
                    <Link
                      href="/login"
                      className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
                    >
                      立即登录
                    </Link>
                  </span>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
