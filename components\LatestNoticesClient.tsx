"use client";

import Link from "next/link";
import { useState, useEffect } from "react";

function formatDate(date: string) {
  if (!date || date.length !== 8) return date;
  return `${date.slice(0, 4)}-${date.slice(4, 6)}-${date.slice(6, 8)}`;
}

// 根据公告类型生成对应的URL路径
function getNoticeUrl(notice: Notice) {
  const typeUrlMap: Record<string, string> = {
    招标计划: "/notices/plan",
    招标公告: "/bid-notice",
    中标结果: "/bid-result",
    在建项目: "/project",
  };

  const urlType = typeUrlMap[notice.notice_third_type_desc] || "/notices/plan";
  return `${urlType}/${notice.id}`;
}

interface Notice {
  id: number;
  notice_title: string;
  notice_third_type_desc: string;
  province: string;
  publish_date: string;
}

interface Tab {
  label: string;
  type: string;
}

interface LatestNoticesProps {
  tabs: Tab[];
  noticesByType: Record<string, Notice[]>;
}

function useIsMobile() {
  const [isMobile, setIsMobile] = useState(false);
  useEffect(() => {
    const check = () => setIsMobile(window.innerWidth < 768);
    check();
    window.addEventListener("resize", check);
    return () => window.removeEventListener("resize", check);
  }, []);
  return isMobile;
}

export default function LatestNoticesClient({
  tabs,
  noticesByType,
}: LatestNoticesProps) {
  const isMobile = useIsMobile();
  const [activeTab, setActiveTab] = useState(tabs[0].type);

  // 合并所有分类的最新9条数据
  const allNotices = Object.values(noticesByType).flat();
  // 如需去重可用：Array.from(new Map(allNotices.map(n => [n.id, n])).values());

  const notices = isMobile ? allNotices : noticesByType[activeTab] || [];

  return (
    <div className="mt-8 dark:text-gray-100">
      <h2 className="text-2xl font-semibold mb-6 text-center">最新公告信息</h2>
      {/* PC端显示tab，移动端不显示 */}
      {!isMobile && (
        <div className="flex justify-center border-b border-gray-200 dark:border-gray-700 mb-6">
          {tabs.map((tab) => (
            <button
              key={tab.type}
              onClick={() => setActiveTab(tab.type)}
              className={`px-6 py-2 font-medium text-lg focus:outline-none transition-colors border-b-2 ${
                activeTab === tab.type
                  ? "border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400"
                  : "border-transparent text-gray-500 dark:text-gray-400 hover:text-blue-500"
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      )}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {notices.map((notice) => (
          <div
            key={notice.id}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6 flex flex-col justify-between hover:shadow-md transition-all border border-gray-100 dark:border-gray-700"
          >
            <Link href={getNoticeUrl(notice)} className="block">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-4 line-clamp-2 min-h-[48px]">
                {notice.notice_title}
              </h3>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center">
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded mr-2 ${
                      notice.notice_third_type_desc === "中标结果"
                        ? "bg-red-100 text-red-600"
                        : notice.notice_third_type_desc === "变更公告"
                        ? "bg-yellow-100 text-yellow-600"
                        : notice.notice_third_type_desc === "招标计划"
                        ? "bg-green-100 text-green-700"
                        : "bg-blue-100 text-blue-600"
                    }`}
                  >
                    {notice.notice_third_type_desc}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {notice.province}
                  </span>
                </div>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {formatDate(notice.publish_date)}
                </span>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </div>
  );
}
