import NoticesSearchPage from "./NoticesSearchPage";

export default async function NoticesPage({
  params,
  searchParams,
}: {
  params: Promise<{ page: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const p = await params;
  const s = await searchParams;
  // console.log(p, s)
  return <NoticesSearchPage baseUrl="/notices" params={p} searchParams={s} />;
}
