export interface NoticeDetail {
  id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  notice_third_type_desc: string;
  content: string;
  province: string;
  publish_date: string;
  publish_agency: string;
  region?: string;
  site_name?: string;
  row_guid?: string;
}

export interface BiddingPlan {
  id: number;
  row_guid: string;
  notice_id: string;
  bidding_plan_code: string;
  bidding_plan_name: string;
  bidding_plan_person_liable: string;
  bidding_state: string;
  tenderer_name: string;
  submit_time: string;
  create_time: string;
  send_time: string;
  other_content: string;
  is_combine_bid?: string;
  bidding_agency?: string;
  tenderer_code?: string;
  credit_code?: string;
}

export interface BiddingProject {
  id: number;
  row_guid: string;
  plan_row_guid: string; // 关联到BiddingPlan的row_guid
  project_name: string;
  tender_project_name: string;
  bidding_contents: string;
  tender_project_type: string;
  bidding_project_content: string;
  tender_mode: string;
  bidding_price: string;
  price_currency: string;
  region_code: string;
  regulatory_authority: string;
  bidding_plan_time: string;
  project_code: string;
  invest_project_code: string;
  created_at: string;
}

export interface TenderFile {
  id: number;
  attach_guid: string;
  row_guid: string;
  file_name: string;
  url: string;
  file_type: string;
  type_name: string;
}
