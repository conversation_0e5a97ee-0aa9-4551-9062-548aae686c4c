import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  staticPageGenerationTimeout: 300,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'img.3lsh.cn',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'auto.aimazing.site',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'directus-markdown2image.aimazing.site',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'vps.1kcode.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3000',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
    ],
  },
};
export default nextConfig;
