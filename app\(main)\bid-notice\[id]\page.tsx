import { notFound } from "next/navigation";
import BidNoticeDetail from "./BidNoticeDetail";
import type {
  BidNoticeDetail as BidNoticeDetailType,
  TenderFile,
} from "./BidNoticeDetail";
import { executeQuery } from "@/lib/db";
import { NoticeDetail } from "@/lib/type";

// 格式化日期时间
function formatDateTime(dateTimeString: string): string {
  if (!dateTimeString) return "";

  // 处理不同的日期时间格式
  try {
    let date: Date;

    // 如果是 YYYYMMDD 格式
    if (dateTimeString.length === 8 && /^\d{8}$/.test(dateTimeString)) {
      const year = dateTimeString.substring(0, 4);
      const month = dateTimeString.substring(4, 6);
      const day = dateTimeString.substring(6, 8);
      date = new Date(`${year}-${month}-${day}`);
      return `${year}-${month}-${day}`;
    }

    // 如果是 YYYYMMDDHHMMSS 格式
    if (dateTimeString.length === 14 && /^\d{14}$/.test(dateTimeString)) {
      const year = dateTimeString.substring(0, 4);
      const month = dateTimeString.substring(4, 6);
      const day = dateTimeString.substring(6, 8);
      const hour = dateTimeString.substring(8, 10);
      const minute = dateTimeString.substring(10, 12);
      const second = dateTimeString.substring(12, 14);
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }

    // 尝试解析为标准日期格式
    date = new Date(dateTimeString);

    if (isNaN(date.getTime())) {
      return dateTimeString; // 如果解析失败，返回原始字符串
    }

    // 返回格式化的日期时间
    return date.toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  } catch (error) {
    return dateTimeString;
  }
}

// Function to get notice data by ID
async function getNoticeDetail(id: string): Promise<NoticeDetail | null> {
  try {
    const results = await executeQuery<NoticeDetail[]>(
      `SELECT * FROM trading_notices WHERE id = ?`,
      [id]
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error("Error fetching notice detail:", error);
    return null;
  }
}

// 获取招标公告详情
async function getBidNoticeDetail(
  id: string
): Promise<BidNoticeDetailType | null> {
  try {
    const results = await executeQuery<BidNoticeDetailType[]>(
      "SELECT * FROM bid_notice_details WHERE notice_id = ?",
      [id]
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error("Error fetching bid notice detail:", error);
    return null;
  }
}

// 获取相关文件
async function getBidNoticeFiles(rowGuid: string): Promise<TenderFile[]> {
  try {
    const results = await executeQuery<TenderFile[]>(
      "SELECT * FROM tender_files WHERE row_guid = ?",
      [rowGuid]
    );

    return results;
  } catch (error) {
    console.error("Error fetching bid notice files:", error);
    return [];
  }
}

export default async function BidNoticePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const notice = await getNoticeDetail(id);
  const bidNotice = await getBidNoticeDetail(notice?.notice_id || "");

  console.log(bidNotice, notice);
  if (!bidNotice) {
    notFound();
  }

  // 获取相关文件
  const files = await getBidNoticeFiles(bidNotice.row_guid);

  // 格式化时间
  const formattedNoticeTime = formatDateTime(bidNotice.notice_send_time || "");
  const formattedOpenTime = formatDateTime(bidNotice.bid_open_time || "");
  const formattedDocGetStartTime = formatDateTime(
    bidNotice.doc_get_start_time || ""
  );
  const formattedDocGetEndTime = formatDateTime(
    bidNotice.doc_get_end_time || ""
  );
  const formattedReferEndTime = formatDateTime(
    bidNotice.bid_doc_refer_end_time || ""
  );

  return (
    <BidNoticeDetail
      bidNotice={bidNotice}
      files={files}
      formattedNoticeTime={formattedNoticeTime}
      formattedOpenTime={formattedOpenTime}
      formattedDocGetStartTime={formattedDocGetStartTime}
      formattedDocGetEndTime={formattedDocGetEndTime}
      formattedReferEndTime={formattedReferEndTime}
    />
  );
}

// 生成静态参数（可选）
export async function generateStaticParams() {
  // 这里可以预生成一些常用的页面参数
  return [];
}

// 页面元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const notice = await getNoticeDetail(id);
  const bidNotice = await getBidNoticeDetail(notice?.notice_id || "");

  if (!bidNotice) {
    return {
      title: "招标公告详情",
    };
  }

  return {
    title: `${
      bidNotice.notice_name || bidNotice.tender_project_name || "招标公告详情"
    }`,
    description: `招标公告详情 - ${bidNotice.tenderer_name || ""}`,
  };
}
