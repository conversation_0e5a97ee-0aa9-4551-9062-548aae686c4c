import Link from "next/link";
import { AlertCircle } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg px-8 py-12 flex flex-col items-center max-w-md w-full">
        <div className="flex flex-col items-center">
          <div className="bg-blue-100 dark:bg-blue-900 rounded-full p-4 mb-4">
            <AlertCircle className="w-12 h-12 text-blue-500 dark:text-blue-400" />
          </div>
          <h1 className="text-6xl font-extrabold text-blue-600 dark:text-blue-400 mb-2">404</h1>
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-2">页面未找到</h2>
          <p className="text-gray-500 dark:text-gray-400 mb-6">您访问的页面不存在或已被移除</p>
        </div>
        <Link
          href="/"
          className="w-full py-3 text-center font-medium text-white bg-blue-600 dark:bg-blue-500 rounded-md hover:bg-blue-700 dark:hover:bg-blue-400 transition-colors text-lg mb-4"
        >
          返回首页
        </Link>
        <div className="text-sm text-gray-400 dark:text-gray-500 mt-2 text-center">
          如果您认为这是一个错误，请联系管理员
        </div>
      </div>
    </div>
  );
}
