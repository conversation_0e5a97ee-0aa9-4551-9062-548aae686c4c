import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

// Types for region data
interface Region {
  id: number;
  code: string;
  name: string;
  parent_code: string | null;
  level: number;
  sort_order: number;
  is_active: boolean;
  children?: Region[];
}

// GET - Fetch regions data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level');
    const parent_code = searchParams.get('parent_code');
    const include_children = searchParams.get('include_children') === 'true';

    let query = `
      SELECT id, code, name, parent_code, level, sort_order, is_active
      FROM regions 
      WHERE is_active = 1
    `;
    const params: any[] = [];

    // Filter by level if specified
    if (level) {
      query += ' AND level = ?';
      params.push(parseInt(level));
    }

    // Filter by parent_code if specified
    if (parent_code) {
      query += ' AND parent_code = ?';
      params.push(parent_code);
    } else if (level === '1' || (!level && !parent_code)) {
      // If requesting provinces or no specific filter, get top-level regions
      query += ' AND parent_code IS NULL';
    }

    query += ' ORDER BY sort_order ASC, name ASC';

    const regions = await executeQuery<Region[]>(query, params);

    // If include_children is true and we're getting provinces, also fetch their cities
    if (include_children && (level === '1' || (!level && !parent_code))) {
      const regionsWithChildren = await Promise.all(
        regions.map(async (region) => {
          const children = await executeQuery<Region[]>(
            `SELECT id, code, name, parent_code, level, sort_order, is_active
             FROM regions 
             WHERE parent_code = ? AND is_active = 1
             ORDER BY sort_order ASC, name ASC`,
            [region.code]
          );
          return {
            ...region,
            children: children
          };
        })
      );
      
      return NextResponse.json({
        success: true,
        data: regionsWithChildren
      });
    }

    return NextResponse.json({
      success: true,
      data: regions
    });
  } catch (error) {
    console.error('Error fetching regions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch regions' },
      { status: 500 }
    );
  }
}

// POST - Add new region (admin only)
export async function POST(request: NextRequest) {
  try {
    // Note: In a real application, you would verify admin permissions here
    const body = await request.json();
    
    const { code, name, parent_code, level, sort_order } = body;

    if (!code || !name || !level) {
      return NextResponse.json(
        { error: 'Code, name, and level are required' },
        { status: 400 }
      );
    }

    // Check if region code already exists
    const existing = await executeQuery<any[]>(
      'SELECT id FROM regions WHERE code = ?',
      [code]
    );

    if (existing.length > 0) {
      return NextResponse.json(
        { error: 'Region code already exists' },
        { status: 400 }
      );
    }

    // Insert new region
    const result = await executeQuery(
      `INSERT INTO regions (code, name, parent_code, level, sort_order, is_active)
       VALUES (?, ?, ?, ?, ?, 1)`,
      [code, name, parent_code || null, level, sort_order || 0]
    );

    return NextResponse.json({
      success: true,
      message: 'Region added successfully',
      data: { id: (result as any).insertId }
    });
  } catch (error) {
    console.error('Error adding region:', error);
    return NextResponse.json(
      { error: 'Failed to add region' },
      { status: 500 }
    );
  }
}

// PUT - Update region (admin only)
export async function PUT(request: NextRequest) {
  try {
    // Note: In a real application, you would verify admin permissions here
    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Region ID is required' },
        { status: 400 }
      );
    }

    // Check if region exists
    const existing = await executeQuery<any[]>(
      'SELECT id FROM regions WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return NextResponse.json(
        { error: 'Region not found' },
        { status: 404 }
      );
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined && ['code', 'name', 'parent_code', 'level', 'sort_order', 'is_active'].includes(key)) {
        updateFields.push(`${key} = ?`);
        updateValues.push(updateData[key]);
      }
    });

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No valid fields to update' },
        { status: 400 }
      );
    }

    updateValues.push(id);

    await executeQuery(
      `UPDATE regions SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      updateValues
    );

    return NextResponse.json({
      success: true,
      message: 'Region updated successfully'
    });
  } catch (error) {
    console.error('Error updating region:', error);
    return NextResponse.json(
      { error: 'Failed to update region' },
      { status: 500 }
    );
  }
}

// DELETE - Deactivate region (admin only)
export async function DELETE(request: NextRequest) {
  try {
    // Note: In a real application, you would verify admin permissions here
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Region ID is required' },
        { status: 400 }
      );
    }

    // Check if region exists
    const existing = await executeQuery<any[]>(
      'SELECT id FROM regions WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return NextResponse.json(
        { error: 'Region not found' },
        { status: 404 }
      );
    }

    // Soft delete by setting is_active to false
    await executeQuery(
      'UPDATE regions SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );

    return NextResponse.json({
      success: true,
      message: 'Region deactivated successfully'
    });
  } catch (error) {
    console.error('Error deactivating region:', error);
    return NextResponse.json(
      { error: 'Failed to deactivate region' },
      { status: 500 }
    );
  }
}
