"use client";

import { useState, useEffect } from "react";
import LatestNoticesClient from "./LatestNoticesClient";

interface Notice {
  id: number;
  notice_title: string;
  notice_third_type_desc: string;
  province: string;
  publish_date: string;
}

const TABS = [
  { label: "招标计划", type: "招标计划" },
  { label: "招标公告", type: "招标公告" },
  { label: "中标结果", type: "中标结果" },
  { label: "拟在建项目", type: "在建项目" },
];

export default function LatestNotices() {
  const [noticesByType, setNoticesByType] = useState<Record<string, Notice[]>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchNotices = async () => {
      try {
        const response = await fetch('/api/notices/latest');
        if (response.ok) {
          const data = await response.json();
          setNoticesByType(data.noticesByType || {});
        }
      } catch (error) {
        console.error('Failed to fetch latest notices:', error);
        // 设置空数据以避免错误
        const emptyData: Record<string, Notice[]> = {};
        TABS.forEach(tab => {
          emptyData[tab.type] = [];
        });
        setNoticesByType(emptyData);
      } finally {
        setLoading(false);
      }
    };

    fetchNotices();
  }, []);

  if (loading) {
    return (
      <div className="py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return <LatestNoticesClient tabs={TABS} noticesByType={noticesByType} />;
}
