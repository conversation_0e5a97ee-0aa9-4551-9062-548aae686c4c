import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

// Types for user settings
interface UserSettings {
  email_notifications: boolean;
  sms_notifications: boolean;
  wechat_notifications: boolean;
  push_notifications: boolean;
  daily_push_times: string[];
  custom_push_settings: any;
  notification_sound: boolean;
  do_not_disturb_start: string;
  do_not_disturb_end: string;
  favorite_regions: string[];
  favorite_categories: string[];
}

// Helper function to verify JWT token and get user ID
async function getUserFromToken(request: NextRequest): Promise<number | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    return decoded.user_id || decoded.id;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// GET - Fetch user settings
export async function GET(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user settings, create default if not exists
    let settings = await executeQuery<any[]>(
      `SELECT 
        email_notifications, sms_notifications, wechat_notifications, 
        push_notifications, daily_push_times, custom_push_settings,
        notification_sound, do_not_disturb_start, do_not_disturb_end,
        favorite_regions, favorite_categories, created_at, updated_at
      FROM user_settings 
      WHERE user_id = ?`,
      [userId]
    );

    if (settings.length === 0) {
      // Create default settings
      await executeQuery(
        `INSERT INTO user_settings (
          user_id, email_notifications, sms_notifications, wechat_notifications,
          push_notifications, daily_push_times, notification_sound,
          do_not_disturb_start, do_not_disturb_end, favorite_regions, favorite_categories
        ) VALUES (?, 1, 1, 1, 1, ?, 1, '22:00:00', '08:00:00', '[]', '[]')`,
        [userId, JSON.stringify(['10:00', '14:00', '17:00'])]
      );

      // Fetch the newly created settings
      settings = await executeQuery<any[]>(
        `SELECT 
          email_notifications, sms_notifications, wechat_notifications, 
          push_notifications, daily_push_times, custom_push_settings,
          notification_sound, do_not_disturb_start, do_not_disturb_end,
          favorite_regions, favorite_categories, created_at, updated_at
        FROM user_settings 
        WHERE user_id = ?`,
        [userId]
      );
    }

    return NextResponse.json({
      success: true,
      data: settings[0]
    });
  } catch (error) {
    console.error('Error fetching user settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch user settings' },
      { status: 500 }
    );
  }
}

// PUT - Update user settings
export async function PUT(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();

    // Check if settings exist
    const existingSettings = await executeQuery<any[]>(
      'SELECT id FROM user_settings WHERE user_id = ?',
      [userId]
    );

    if (existingSettings.length === 0) {
      // Create new settings record
      await executeQuery(
        `INSERT INTO user_settings (
          user_id, email_notifications, sms_notifications, wechat_notifications,
          push_notifications, daily_push_times, custom_push_settings,
          notification_sound, do_not_disturb_start, do_not_disturb_end,
          favorite_regions, favorite_categories
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userId,
          body.email_notifications ? 1 : 0,
          body.sms_notifications ? 1 : 0,
          body.wechat_notifications ? 1 : 0,
          body.push_notifications ? 1 : 0,
          JSON.stringify(body.daily_push_times || ['10:00', '14:00', '17:00']),
          JSON.stringify(body.custom_push_settings || {}),
          body.notification_sound ? 1 : 0,
          body.do_not_disturb_start || '22:00:00',
          body.do_not_disturb_end || '08:00:00',
          JSON.stringify(body.favorite_regions || []),
          JSON.stringify(body.favorite_categories || [])
        ]
      );
    } else {
      // Build update query dynamically
      const updateFields: string[] = [];
      const updateValues: any[] = [];

      Object.keys(body).forEach(key => {
        if (body[key] !== undefined) {
          if (['daily_push_times', 'custom_push_settings', 'favorite_regions', 'favorite_categories'].includes(key)) {
            updateFields.push(`${key} = ?`);
            updateValues.push(JSON.stringify(body[key]));
          } else if (['email_notifications', 'sms_notifications', 'wechat_notifications', 'push_notifications', 'notification_sound'].includes(key)) {
            updateFields.push(`${key} = ?`);
            updateValues.push(body[key] ? 1 : 0);
          } else {
            updateFields.push(`${key} = ?`);
            updateValues.push(body[key]);
          }
        }
      });

      if (updateFields.length > 0) {
        updateValues.push(userId);
        await executeQuery(
          `UPDATE user_settings SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE user_id = ?`,
          updateValues
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Settings updated successfully'
    });
  } catch (error) {
    console.error('Error updating user settings:', error);
    return NextResponse.json(
      { error: 'Failed to update user settings' },
      { status: 500 }
    );
  }
}

// POST - Reset settings to default
export async function POST(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Reset to default settings
    await executeQuery(
      `INSERT INTO user_settings (
        user_id, email_notifications, sms_notifications, wechat_notifications,
        push_notifications, daily_push_times, notification_sound,
        do_not_disturb_start, do_not_disturb_end, favorite_regions, favorite_categories
      ) VALUES (?, 1, 1, 1, 1, ?, 1, '22:00:00', '08:00:00', '[]', '[]')
      ON DUPLICATE KEY UPDATE
        email_notifications = 1,
        sms_notifications = 1,
        wechat_notifications = 1,
        push_notifications = 1,
        daily_push_times = ?,
        notification_sound = 1,
        do_not_disturb_start = '22:00:00',
        do_not_disturb_end = '08:00:00',
        favorite_regions = '[]',
        favorite_categories = '[]',
        updated_at = CURRENT_TIMESTAMP`,
      [
        userId,
        JSON.stringify(['10:00', '14:00', '17:00']),
        JSON.stringify(['10:00', '14:00', '17:00'])
      ]
    );

    return NextResponse.json({
      success: true,
      message: 'Settings reset to default successfully'
    });
  } catch (error) {
    console.error('Error resetting user settings:', error);
    return NextResponse.json(
      { error: 'Failed to reset user settings' },
      { status: 500 }
    );
  }
}
