"use client";

import { useState, useEffect } from "react";
import { Check, ChevronDown, MapPin, Search } from "lucide-react";

interface Region {
  id: number;
  code: string;
  name: string;
  parent_code: string | null;
  level: number;
  children?: Region[];
}

interface SubscriptionRegionSelectorProps {
  selectedRegions: string[];
  onRegionChange: (regions: string[]) => void;
  placeholder?: string;
  maxSelections?: number;
  showSelectAll?: boolean;
  className?: string;
}

export default function SubscriptionRegionSelector({
  selectedRegions,
  onRegionChange,
  placeholder = "选择地区",
  maxSelections,
  showSelectAll = true,
  className = ""
}: SubscriptionRegionSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [regions, setRegions] = useState<Region[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredRegions, setFilteredRegions] = useState<Region[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadRegions();
  }, []);

  useEffect(() => {
    filterRegions();
  }, [regions, searchTerm]);

  const loadRegions = async () => {
    try {
      const response = await fetch('/api/regions?level=1&include_children=true');
      const data = await response.json();
      if (data.success) {
        setRegions(data.data);
        setFilteredRegions(data.data);
      }
    } catch (error) {
      console.error('Error loading regions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filterRegions = () => {
    if (!searchTerm) {
      setFilteredRegions(regions);
      return;
    }

    const filtered = regions.filter(region => 
      region.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (region.children && region.children.some(child => 
        child.name.toLowerCase().includes(searchTerm.toLowerCase())
      ))
    );

    setFilteredRegions(filtered);
  };

  const handleRegionToggle = (regionName: string) => {
    const isSelected = selectedRegions.includes(regionName);
    let newSelection: string[];

    if (isSelected) {
      newSelection = selectedRegions.filter(r => r !== regionName);
    } else {
      if (maxSelections && selectedRegions.length >= maxSelections) {
        alert(`最多只能选择 ${maxSelections} 个地区`);
        return;
      }
      newSelection = [...selectedRegions, regionName];
    }

    onRegionChange(newSelection);
  };

  const handleSelectAll = () => {
    if (selectedRegions.length === regions.length) {
      onRegionChange([]);
    } else {
      onRegionChange(regions.map(r => r.name));
    }
  };

  const handleProvinceToggle = (province: Region) => {
    const provinceSelected = selectedRegions.includes(province.name);
    const childrenSelected = province.children?.filter(child => 
      selectedRegions.includes(child.name)
    ) || [];

    if (provinceSelected || childrenSelected.length > 0) {
      // Remove province and all its children
      const toRemove = [province.name, ...(province.children?.map(c => c.name) || [])];
      onRegionChange(selectedRegions.filter(r => !toRemove.includes(r)));
    } else {
      // Add province (which represents selecting the whole province)
      if (maxSelections && selectedRegions.length >= maxSelections) {
        alert(`最多只能选择 ${maxSelections} 个地区`);
        return;
      }
      onRegionChange([...selectedRegions, province.name]);
    }
  };

  const getDisplayText = () => {
    if (selectedRegions.length === 0) {
      return placeholder;
    }
    if (selectedRegions.length === 1) {
      return selectedRegions[0];
    }
    if (selectedRegions.length <= 3) {
      return selectedRegions.join(', ');
    }
    return `已选择 ${selectedRegions.length} 个地区`;
  };

  const isProvinceSelected = (province: Region) => {
    return selectedRegions.includes(province.name);
  };

  const isProvincePartiallySelected = (province: Region) => {
    if (!province.children) return false;
    const selectedChildren = province.children.filter(child => 
      selectedRegions.includes(child.name)
    );
    return selectedChildren.length > 0 && selectedChildren.length < province.children.length;
  };

  if (isLoading) {
    return (
      <div className={`relative ${className}`}>
        <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500">
          加载地区数据...
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Trigger Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 text-left border border-gray-300 rounded-md shadow-sm bg-white hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <MapPin className="w-4 h-4 text-gray-400 mr-2" />
            <span className={selectedRegions.length === 0 ? 'text-gray-500' : 'text-gray-900'}>
              {getDisplayText()}
            </span>
          </div>
          <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </div>
      </button>

      {/* Dropdown Panel */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg">
          <div className="p-3 border-b border-gray-200">
            {/* Search */}
            <div className="relative mb-3">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="搜索地区..."
                className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Select All */}
            {showSelectAll && (
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="select-all"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  checked={selectedRegions.length === regions.length}
                  onChange={handleSelectAll}
                />
                <label htmlFor="select-all" className="ml-2 text-sm font-medium text-gray-700">
                  默认全国
                </label>
                <button
                  type="button"
                  onClick={() => onRegionChange([])}
                  className="ml-4 text-sm text-blue-600 hover:text-blue-500"
                >
                  反选
                </button>
              </div>
            )}
          </div>

          {/* Region List */}
          <div className="max-h-64 overflow-y-auto">
            {filteredRegions.length > 0 ? (
              <div className="p-2">
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
                  {filteredRegions.map((province) => (
                    <div key={province.code} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`region-${province.code}`}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        checked={selectedRegions.includes(province.name)}
                        onChange={() => handleRegionToggle(province.name)}
                      />
                      <label 
                        htmlFor={`region-${province.code}`}
                        className="ml-2 text-sm text-gray-700 cursor-pointer"
                      >
                        {province.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="p-4 text-center text-sm text-gray-500">
                没有找到匹配的地区
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">
                已选择 {selectedRegions.length} 个地区
                {maxSelections && ` / ${maxSelections}`}
              </span>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                确定
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
