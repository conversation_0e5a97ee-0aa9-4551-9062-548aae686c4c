"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MessageSquare, Plus, Star, Calendar, User } from "lucide-react";
import userService from "@/lib/userService";

interface Feedback {
  id: number;
  type: string;
  title: string;
  content: string;
  rating: number;
  status: string;
  created_at: string;
  updated_at: string;
}

export default function ProfileFeedbackPage() {
  const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchMyFeedbacks();
  }, []);

  const fetchMyFeedbacks = async () => {
    try {
      setLoading(true);
      const token = userService.getAccessToken();
      if (!token) return;

      const response = await fetch('/api/user/feedback', {
        headers: {
          'Authorization': `<PERSON><PERSON> ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setFeedbacks(data.feedbacks || []);
      }
    } catch (error) {
      console.error('Failed to fetch feedbacks:', error);
    } finally {
      setLoading(false);
    }
  };

  const getTypeLabel = (type: string) => {
    const types: Record<string, string> = {
      suggestion: "功能建议",
      bug: "问题反馈",
      complaint: "投诉建议",
      praise: "表扬建议",
      other: "其他"
    };
    return types[type] || type;
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { label: string; variant: any }> = {
      submitted: { label: "已提交", variant: "default" },
      received: { label: "已收到", variant: "secondary" }
    };

    const config = statusConfig[status] || { label: status, variant: "secondary" };
    return <Badge variant={config.variant}>{config.label}</Badge>;
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
        <span className="ml-1 text-sm text-gray-600 dark:text-gray-400">{rating}</span>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">我的反馈</h1>
            <p className="text-gray-600 dark:text-gray-400">查看和管理您提交的反馈</p>
          </div>
        </div>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">我的反馈</h1>
          <p className="text-gray-600 dark:text-gray-400">查看和管理您提交的反馈</p>
        </div>
        <Link href="/profile/feedback/new">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            提交新反馈
          </Button>
        </Link>
      </div>

      {feedbacks.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <MessageSquare className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              暂无反馈记录
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-center mb-4">
              您还没有提交过任何反馈，快去分享您的想法和建议吧！
            </p>
            <Link href="/profile/feedback/new">
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                提交反馈
              </Button>
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {feedbacks.map((feedback) => (
            <Card key={feedback.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{getTypeLabel(feedback.type)}</Badge>
                      {getStatusBadge(feedback.status)}
                    </div>
                    <CardTitle className="text-lg">{feedback.title}</CardTitle>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(feedback.created_at).toLocaleString()}
                      </div>
                      {renderStars(feedback.rating)}
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 dark:text-gray-300 mb-4">{feedback.content}</p>
                <div className="mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
                  <p className="text-sm text-green-800 dark:text-green-200">
                    感谢您的反馈！我们已收到您的建议，会认真考虑并持续改进产品。
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
