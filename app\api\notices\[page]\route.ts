import { NextResponse } from "next/server";
import { executeQuery } from "@/lib/db";

export async function GET(
  request: Request,
  { params }: { params: Promise<{ page: string }> }
) {
  try {
    const page = parseInt((await params).page) || 1;
    const pageSize = 25;
    const offset = (page - 1) * pageSize;

    // Get paginated notices
    const notices = await executeQuery<any[]>(
      `SELECT 
        id, 
        notice_id, 
        notice_title, 
        notice_type_desc, 
        province, 
        publish_date 
      FROM trading_notices 
      ORDER BY publish_date DESC, id DESC 
      LIMIT ? OFFSET ?`,
      [pageSize, offset]
    );

    // Format date for display
    const formattedNotices = notices.map((notice) => ({
      ...notice,
      publishDate: formatDate(notice.publish_date),
    }));

    return NextResponse.json({ notices: formattedNotices });
  } catch (error) {
    console.error("Error fetching notices:", error);
    return NextResponse.json(
      { error: "Failed to fetch notices" },
      { status: 500 }
    );
  }
}

// Helper function to format date from YYYYMMDD to YYYY-MM-DD
function formatDate(dateString: string): string {
  if (!dateString || dateString.length !== 8) return "";
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  return `${year}-${month}-${day}`;
}
