"use client";

import { Ch<PERSON>ronLeft, Printer, Heart, Download } from "lucide-react";
import { useState } from "react";
import { File } from "lucide-react";
import Link from "next/link";
import {
  BiddingPlan,
  BiddingProject,
  NoticeDetail,
  TenderFile,
} from "@/lib/type";
import PrintButton from "@/components/PrintButton";
import userService from "@/lib/userService";
import PlanDetailPrintStyles from "@/components/PlanDetailPrintStyles";
import { exportElementToPDF, sanitizeFilename } from "@/lib/pdfExportUtils";

// Client component for tabs functionality
export default function PlanDetail({
  notice,
  plans,
  projects,
  files,
  formattedPublishDate,
  formattedSubmitTime,
  formattedCreateTime,
  formattedSendTime,
}: {
  notice: NoticeDetail;
  plans: BiddingPlan[];
  projects: BiddingProject[];
  files: TenderFile[];
  formattedPublishDate: string;
  formattedSubmitTime: string;
  formattedCreateTime: string;
  formattedSendTime: string;
}) {
  const [activePlanIndex, setActivePlanIndex] = useState(0);
  const [isFavorited, setIsFavorited] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 根据当前选中的计划过滤项目
  const activePlan = plans.length > 0 ? plans[activePlanIndex] : null;
  const planProjects = activePlan
    ? projects.filter(
        (project) => project.plan_row_guid === activePlan.row_guid
      )
    : [];

  // 根据计划的row_guid过滤相关文件
  const planFiles = files.filter(
    (file) => activePlan && file.row_guid === activePlan.row_guid
  );

  // 检查用户登录状态
  const checkLoginStatus = () => {
    const token = userService.getAccessToken();
    return !!token;
  };

  // 收藏功能
  const handleFavorite = async () => {
    if (!checkLoginStatus()) {
      alert("请先登录后再收藏");
      // 可以跳转到登录页面
      window.location.href = "/login";
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch("/api/favorites", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${userService.getAccessToken()}`
        },
        body: JSON.stringify({
          notice_id: notice.id,
          notice_title: notice.notice_title,
          notice_type: "plan",
          content_preview: notice.content?.substring(0, 200) || ""
        })
      });

      const data = await response.json();

      if (data.success) {
        setIsFavorited(true);
        alert("收藏成功！");
      } else {
        alert(data.message || "收藏失败，请重试");
      }
    } catch (error) {
      console.error("收藏失败:", error);
      alert("收藏失败，请重试");
    } finally {
      setIsLoading(false);
    }
  };

  // PDF导出功能 - 使用优化的导出工具
  const handleExportPDF = async () => {
    if (!checkLoginStatus()) {
      alert("请先登录后再导出PDF");
      window.location.href = "/login";
      return;
    }

    setIsExporting(true);
    try {
      const filename = sanitizeFilename(`${notice.notice_title}_招标计划.pdf`);

      await exportElementToPDF({
        selector: '.pdf-export-area',
        filename,
        title: notice.notice_title,
        scale: 2,
        quality: 0.95
      });

    } catch (error) {
      console.error("PDF导出失败:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('oklch')) {
        alert("PDF导出失败：浏览器不支持某些颜色格式，请尝试使用其他浏览器或更新浏览器版本");
      } else if (errorMessage.includes('未找到选择器')) {
        alert("PDF导出失败：未找到可导出的内容区域");
      } else {
        alert(`PDF导出失败：${errorMessage || '未知错误'}，请重试`);
      }
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <PlanDetailPrintStyles />
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Desktop Header */}
      <div className="hidden md:block bg-white dark:bg-gray-800 border-b">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            招标计划详情
          </h1>
        </div>
      </div>

      {/* Mobile Header */}
      <div className="md:hidden bg-blue-500 text-white p-4 flex items-center">
        <Link href="/notices/1" className="mr-2">
          <ChevronLeft className="h-6 w-6" />
        </Link>
        <h1 className="text-xl font-medium text-center flex-1">招标计划详情</h1>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8 print-content-area pdf-export-area">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          {/* 详情页标题部分 */}
          <div className="px-4 py-6 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-start">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {notice.notice_title}
              </h2>
              <div className="hidden md:flex space-x-2">
                <PrintButton
                  printTitle={notice.notice_title}
                  variant="outline"
                  size="sm"
                />

                <button
                  onClick={handleFavorite}
                  disabled={isLoading || isFavorited}
                  className={`inline-flex items-center px-3 py-2 border shadow-sm text-sm leading-4 font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors ${
                    isFavorited
                      ? 'border-red-300 text-red-700 bg-red-50 hover:bg-red-100 focus:ring-red-500'
                      : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500'
                  } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                >
                  <Heart className={`h-4 w-4 mr-1 ${isFavorited ? 'fill-current' : ''}`} />
                  {isLoading ? '处理中...' : isFavorited ? '已收藏' : '收藏'}
                </button>

                <button
                  onClick={handleExportPDF}
                  disabled={isExporting}
                  className={`inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors ${
                    isExporting ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <Download className={`h-4 w-4 mr-1 ${isExporting ? 'animate-bounce' : ''}`} />
                  {isExporting ? '导出中...' : '导出PDF'}
                </button>
              </div>
            </div>
            <div className="mt-2 flex flex-wrap items-center text-sm text-gray-500 dark:text-gray-400">
              <div className="mr-6 mb-2">
                <span className="font-medium">发布时间：</span>
                {formattedPublishDate}
              </div>
              <div className="mr-6 mb-2">
                <span className="font-medium">所在地区：</span>
                {notice.province}
              </div>
              <div className="mr-6 mb-2">
                <span className="font-medium">公告类型：</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {notice.notice_third_type_desc}
                </span>
              </div>
              {plans.length > 0 && plans[0].bidding_plan_code && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">计划编号：</span>
                  {plans[0].bidding_plan_code}
                </div>
              )}
              {notice.publish_agency && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">发布机构：</span>
                  {notice.publish_agency}
                </div>
              )}
            </div>
          </div>

          {/* 详情页内容部分 */}
          <div className="px-4 py-6 sm:px-6">
            {/* 内容区域 - 左右两栏布局 */}
            <div className="py-6 grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* 左侧内容区域 */}
              <div className="md:col-span-3 prose max-w-none dark:prose-invert">
                {/* 公告内容 */}
                {/* {notice.content ? (
                                    <div dangerouslySetInnerHTML={{ __html: notice.content }} />
                                ) : (
                                    <p className="text-gray-500 dark:text-gray-400">暂无内容</p>
                                )} */}

                {/* 计划信息 */}
                {plans.length > 0 && (
                  <div className="border-gray-200 pt-2">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      招标计划信息
                    </h3>

                    {/* 计划切换选项卡 */}
                    {plans.length > 1 && (
                      <div className="mb-4 border-b border-gray-200 dark:border-gray-700">
                        <div className="flex overflow-x-auto">
                          {plans.map((plan, index) => (
                            <button
                              key={index}
                              onClick={() => setActivePlanIndex(index)}
                              className={`py-2 px-4 text-sm font-medium ${
                                activePlanIndex === index
                                  ? "bg-blue-100 text-blue-700 border-b-2 border-blue-500"
                                  : "text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                              }`}
                            >
                              {plan.bidding_plan_code || `计划 ${index + 1}`}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {activePlan && (
                      <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                          <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                            <tr>
                              <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                                合并招标
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                {activePlan.is_combine_bid}
                              </td>
                              <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                                性质
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                {activePlan.bidding_state}
                              </td>
                            </tr>
                            {activePlan.bidding_plan_name && (
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标计划名称
                                </td>
                                <td
                                  className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                                  colSpan={3}
                                >
                                  {activePlan.bidding_plan_name}
                                </td>
                              </tr>
                            )}
                            {activePlan.tenderer_name && (
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标计划发布人
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {activePlan.bidding_plan_person_liable}
                                </td>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  发布时间
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {activePlan.send_time}
                                </td>
                              </tr>
                            )}
                            {activePlan.bidding_agency && (
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标人名称
                                </td>
                                <td
                                  className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                                  colSpan={3}
                                >
                                  {activePlan.bidding_agency}
                                </td>
                              </tr>
                            )}
                            {activePlan.tenderer_code && (
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标人名称
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {activePlan.tenderer_name}
                                </td>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标人统一社会信用代码
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {activePlan.tenderer_code || "-"}
                                </td>
                              </tr>
                            )}
                            {activePlan.bidding_plan_code && (
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  备注
                                </td>
                                <td
                                  className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                                  colSpan={3}
                                >
                                  无
                                </td>
                              </tr>
                            )}
                            <tr>
                              <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                招标计划附件
                              </td>
                              <td
                                className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100 hover:text-blue-600"
                                colSpan={3}
                              >
                                {/* 附件资料部分 */}
                                <div>
                                  {planFiles.length > 0 ? (
                                    <ul className="divide-y divide-gray-200 dark:divide-gray-700 cursor-pointer">
                                      {planFiles.map((file, index) => (
                                        <li key={index} className="py-4">
                                          <div className="flex items-center space-x-4">
                                            <div className="flex-shrink-0">
                                              <File className="h-6 w-6 text-gray-400" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                                {file.file_name}
                                                {file.file_type}
                                              </p>
                                            </div>
                                            <div>
                                              <a
                                                href={file.url}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                              >
                                                下载
                                              </a>
                                            </div>
                                          </div>
                                        </li>
                                      ))}
                                    </ul>
                                  ) : (
                                    <p className="text-gray-500 dark:text-gray-400">
                                      暂无附件
                                    </p>
                                  )}
                                </div>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                )}

                {/* 项目信息 */}
                {planProjects.length > 0 && (
                  <div className="mt-6 border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      项目信息
                    </h3>
                    {planProjects.map((project, index) => (
                      <div
                        key={index}
                        className={
                          index > 0
                            ? "mt-8 pt-8 border-t border-gray-200 dark:border-gray-600"
                            : ""
                        }
                      >
                        <h4 className="text-md font-medium text-blue-700 dark:text-blue-400 mb-4">
                          招标项目 {index + 1}
                        </h4>
                        <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                              {project.tender_project_name && (
                                <tr>
                                  <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                                    招标项目名称
                                  </td>
                                  <td
                                    className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                                    colSpan={3}
                                  >
                                    {project.tender_project_name}
                                  </td>
                                </tr>
                              )}
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  是否依法必招项目
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  是
                                </td>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                                  投资项目代码
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {project.invest_project_code || ""}
                                </td>
                              </tr>
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标项目类型
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {project.tender_project_type || ""}
                                </td>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标方式
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {project.tender_mode || ""}
                                </td>
                              </tr>
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标内容
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {project.bidding_contents || ""}
                                </td>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  预估发包价（元）
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {project.bidding_price || ""}
                                </td>
                              </tr>
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标项目建设地点
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {project.region_code || ""}
                                </td>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标监督部门
                                </td>
                                <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                                  {project.regulatory_authority || ""}
                                </td>
                              </tr>
                              <tr>
                                <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                  招标公告预计发布时间
                                </td>
                                <td
                                  className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                                  colSpan={3}
                                >
                                  {project.bidding_plan_time || ""}
                                </td>
                              </tr>
                              {project.bidding_project_content && (
                                <tr>
                                  <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                                    项目概况
                                  </td>
                                  <td
                                    className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                                    colSpan={3}
                                  >
                                    {project.bidding_project_content}
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8 border-t border-gray-200 pt-6">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      基本信息
                    </h3>
                    <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                      {notice.site_name && (
                        <div className="sm:col-span-1">
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            来源网站
                          </dt>
                          <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                            {notice.site_name}
                          </dd>
                        </div>
                      )}
                      {notice.region && (
                        <div className="sm:col-span-1">
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                            区域
                          </dt>
                          <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                            {notice.region}
                          </dd>
                        </div>
                      )}
                    </dl>
                  </div>

                  {/* 相关链接 */}
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-4">
                      相关推荐
                    </h3>
                    <ul className="space-y-2">
                      <li>
                        <Link
                          href="/notices/1?noticeType=招标计划"
                          className="text-blue-600 hover:underline dark:text-blue-400"
                        >
                          查看更多招标计划
                        </Link>
                      </li>
                      <li>
                        <Link
                          href="/notices/1"
                          className="text-blue-600 hover:underline dark:text-blue-400"
                        >
                          返回公告列表3
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* 右侧区域 - 招标热词 */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 sticky top-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    招标热词
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer">
                      建筑工程
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer">
                      市政工程
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-purple-100 text-purple-800 hover:bg-purple-200 cursor-pointer">
                      设计方案
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-yellow-100 text-yellow-800 hover:bg-yellow-200 cursor-pointer">
                      项目咨询
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-red-100 text-red-800 hover:bg-red-200 cursor-pointer">
                      工程项目
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800 hover:bg-indigo-200 cursor-pointer">
                      车辆采购
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-pink-100 text-pink-800 hover:bg-pink-200 cursor-pointer">
                      服务外包
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-gray-100 text-gray-800 hover:bg-gray-200 cursor-pointer">
                      政府采购
                    </span>
                  </div>

                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mt-6 mb-3">
                    热门公告
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                      >
                        广州市车辆绿化工程及设施采购招标公告
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                      >
                        2025年江苏省交通设施维护工程招标
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                      >
                        北京市公园绿化管理服务项目招标
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                      >
                        上海市车站商业综合体建设项目招标
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
    </>
  );
}
