"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import userService from "@/lib/userService";
import { 
  Heart, 
  Search, 
  Filter,
  Calendar,
  Building,
  FileText,
  Trash2,
  ExternalLink,
  Eye
} from "lucide-react";

interface Favorite {
  id: number;
  user_id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  publish_date: string;
  site_name: string;
  created_at: string;
  notice_content?: string;
  publish_agency?: string;
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export default function FavoritesPage() {
  const [favorites, setFavorites] = useState<Favorite[]>([]);
  const [pagination, setPagination] = useState<PaginationData>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: '',
    notice_type: ''
  });

  useEffect(() => {
    loadFavorites();
  }, [pagination.page, filters]);

  const loadFavorites = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.notice_type) {
        params.append('notice_type', filters.notice_type);
      }

      const response = await fetch(`/api/user/favorites?${params}`, {
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setFavorites(data.data.favorites);
        setPagination(data.data.pagination);
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const removeFavorite = async (favoriteId: number) => {
    if (!confirm('确定要取消收藏吗？')) {
      return;
    }

    try {
      const response = await fetch(`/api/user/favorites?id=${favoriteId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        setFavorites(prev => prev.filter(fav => fav.id !== favoriteId));
        setPagination(prev => ({ ...prev, total: prev.total - 1 }));
      } else {
        alert('取消收藏失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error removing favorite:', error);
      alert('取消收藏失败，请重试');
    }
  };

  const getNoticeTypeColor = (type: string) => {
    switch (type) {
      case '招标公告':
        return 'bg-blue-100 text-blue-800';
      case '中标公告':
        return 'bg-green-100 text-green-800';
      case '变更公告':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200';
    }
  };

  if (isLoading && favorites.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">我的收藏</h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          管理您收藏的招投标信息
        </p>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-6">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="搜索标题或发布机构..."
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-800 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>

            {/* Type Filter */}
            <select
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              value={filters.notice_type}
              onChange={(e) => handleFilterChange('notice_type', e.target.value)}
            >
              <option value="">全部类型</option>
              <option value="招标公告">招标公告</option>
              <option value="中标公告">中标公告</option>
              <option value="变更公告">变更公告</option>
            </select>

            {/* Results Count */}
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
              <Filter className="w-4 h-4 mr-2" />
              共 {pagination.total} 条收藏
            </div>
          </div>
        </div>
      </div>

      {/* Favorites List */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        {favorites.length > 0 ? (
          <>
            <div className="divide-y divide-gray-200">
              {favorites.map((favorite) => (
                <div key={favorite.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getNoticeTypeColor(favorite.notice_type_desc)}`}>
                          {favorite.notice_type_desc}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          收藏于 {new Date(favorite.created_at).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                      
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2 hover:text-blue-600 dark:text-blue-400 cursor-pointer">
                        {favorite.notice_title}
                      </h3>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-3">
                        <span className="flex items-center">
                          <Building className="w-4 h-4 mr-1" />
                          {favorite.publish_agency || favorite.site_name}
                        </span>
                        <span className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {new Date(favorite.publish_date).toLocaleDateString('zh-CN')}
                        </span>
                      </div>
                      
                      {favorite.notice_content && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2">
                          {favorite.notice_content.length > 150 
                            ? `${favorite.notice_content.substring(0, 150)}...` 
                            : favorite.notice_content
                          }
                        </p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 ml-4">
                      <button
                        onClick={() => {
                          // 这里可以跳转到详情页
                          window.open(`/notices/${favorite.notice_id}`, '_blank');
                        }}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-blue-700 bg-blue-100 hover:bg-blue-200"
                      >
                        <Eye className="w-4 h-4 mr-1" />
                        查看
                      </button>
                      
                      <button
                        onClick={() => removeFavorite(favorite.id)}
                        className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium text-red-700 bg-red-100 hover:bg-red-200"
                      >
                        <Trash2 className="w-4 h-4 mr-1" />
                        取消收藏
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    显示第 {((pagination.page - 1) * pagination.limit) + 1} - {Math.min(pagination.page * pagination.limit, pagination.total)} 条，
                    共 {pagination.total} 条记录
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.hasPrev}
                      className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 disabled:bg-gray-100 dark:bg-gray-700 disabled:text-gray-400"
                    >
                      上一页
                    </button>
                    <span className="px-3 py-1 text-sm text-gray-700 dark:text-gray-300">
                      第 {pagination.page} / {pagination.totalPages} 页
                    </span>
                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.hasNext}
                      className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:bg-gray-700 disabled:bg-gray-100 dark:bg-gray-700 disabled:text-gray-400"
                    >
                      下一页
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="text-center py-12">
            <Heart className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无收藏</h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              {Object.values(filters).some(v => v) 
                ? '没有找到符合条件的收藏，尝试调整筛选条件' 
                : '您还没有收藏任何招投标信息'
              }
            </p>
            {!Object.values(filters).some(v => v) && (
              <div className="mt-6">
                <Link
                  href="/"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600"
                >
                  <FileText className="w-4 h-4 mr-2" />
                  去浏览招投标信息
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
