"use client";

import { useState } from "react";
import userService from "@/lib/userService";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  User,
  Bell,
  BookmarkCheck,
  Settings,
  Crown,
  Heart,
  History,
  ChevronDown,
  ChevronRight,
  Plus,
  List,
  BarChart3,
  MessageSquare,
  MessageCircle,
  Download,
  LogOut
} from "lucide-react";

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href?: string;
  children?: MenuItem[];
}

const menuItems: MenuItem[] = [
  {
    id: "dashboard",
    label: "个人中心",
    icon: <User className="w-5 h-5" />,
    href: "/profile"
  },
  {
    id: "subscriptions",
    label: "我的订阅",
    icon: <BookmarkCheck className="w-5 h-5" />,
    children: [
      {
        id: "subscription-list",
        label: "订阅列表",
        icon: <List className="w-4 h-4" />,
        href: "/profile/subscriptions"
      },
      {
        id: "subscription-new",
        label: "新建订阅",
        icon: <Plus className="w-4 h-4" />,
        href: "/profile/subscriptions/new"
      },
      {
        id: "subscription-stats",
        label: "订阅统计",
        icon: <BarChart3 className="w-4 h-4" />,
        href: "/profile/subscriptions/stats"
      }
    ]
  },
  {
    id: "notifications",
    label: "推送管理",
    icon: <Bell className="w-5 h-5" />,
    children: [
      {
        id: "push-records",
        label: "推送记录",
        icon: <MessageSquare className="w-4 h-4" />,
        href: "/profile/push-records"
      },
      {
        id: "notification-settings",
        label: "推送设置",
        icon: <Settings className="w-4 h-4" />,
        href: "/profile/settings"
      }
    ]
  },
  {
    id: "favorites",
    label: "我的收藏",
    icon: <Heart className="w-5 h-5" />,
    href: "/profile/favorites"
  },
  {
    id: "history",
    label: "浏览记录",
    icon: <History className="w-5 h-5" />,
    href: "/profile/history"
  },
  {
    id: "downloads",
    label: "我的下载",
    icon: <Download className="w-5 h-5" />,
    href: "/profile/downloads"
  },
  {
    id: "membership",
    label: "会员权益",
    icon: <Crown className="w-5 h-5" />,
    href: "/profile/membership"
  },
  {
    id: "feedback",
    label: "意见反馈",
    icon: <MessageCircle className="w-5 h-5" />,
    children: [
      {
        id: "submit-feedback",
        label: "提交反馈",
        icon: <MessageCircle className="w-4 h-4" />,
        href: "/profile/feedback/new"
      },
      {
        id: "my-feedback",
        label: "我的反馈",
        icon: <MessageSquare className="w-4 h-4" />,
        href: "/profile/feedback"
      }
    ]
  }
];



export default function ProfileSidebar() {
  const pathname = usePathname();
  const [expandedItems, setExpandedItems] = useState<string[]>(() => {
    // 根据当前路径自动展开相关菜单
    const expanded: string[] = [];
    menuItems.forEach(item => {
      if (item.children) {
        const hasActiveChild = item.children.some(child =>
          child.href && pathname.startsWith(child.href)
        );
        if (hasActiveChild) {
          expanded.push(item.id);
        }
      }
    });
    return expanded;
  });

  const toggleExpanded = (itemId: string) => {
    setExpandedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const isActive = (href: string) => {
    if (href === "/profile") {
      return pathname === "/profile";
    }
    return pathname.startsWith(href);
  };

  const renderMenuItem = (item: MenuItem, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.id);
    const active = item.href ? isActive(item.href) : false;

    if (hasChildren) {
      return (
        <div key={item.id}>
          <button
            onClick={() => toggleExpanded(item.id)}
            className={`w-full flex items-center justify-between px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
              level > 0 ? 'pl-8' : ''
            }`}
          >
            <div className="flex items-center space-x-3">
              <span className="text-gray-500 dark:text-gray-400">{item.icon}</span>
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">{item.label}</span>
            </div>
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-gray-400" />
            ) : (
              <ChevronRight className="w-4 h-4 text-gray-400" />
            )}
          </button>

          {isExpanded && item.children && (
            <div className="bg-gray-50 dark:bg-gray-700">
              {item.children.map(child => renderMenuItem(child, level + 1))}
            </div>
          )}
        </div>
      );
    }

    return (
      <Link
        key={item.id}
        href={item.href!}
        className={`flex items-center space-x-3 px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors border-r-2 ${
          active
            ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-500 text-blue-700 dark:text-blue-300'
            : 'border-transparent text-gray-700 dark:text-gray-300'
        } ${level > 0 ? 'pl-8' : ''}`}
      >
        <span className={active ? 'text-blue-500 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'}>
          {item.icon}
        </span>
        <span className={`text-sm font-medium ${active ? 'text-blue-700 dark:text-blue-300' : 'text-gray-700 dark:text-gray-300'}`}>
          {item.label}
        </span>
      </Link>
    );
  };

  const handleLogout = () => {
    userService.logout();
    window.location.href = "/";
  };

  return (
    <div className="w-64 bg-white dark:bg-gray-800 shadow-sm border-r border-gray-200 dark:border-gray-700 h-full flex flex-col">
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">个人中心</h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">管理您的账户和设置</p>
      </div>

      <nav className="flex-1 mt-2 overflow-y-auto">
        {menuItems.map(item => renderMenuItem(item))}
      </nav>

      {/* 退出登录 */}
      <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <button
          onClick={handleLogout}
          className="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          <LogOut className="w-4 h-4 mr-2" />
          退出登录
        </button>
      </div>
    </div>
  );
}
