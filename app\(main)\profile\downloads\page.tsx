"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Download, Search, Filter, FileText, Calendar, Building, Trash2, ExternalLink, File } from "lucide-react";
import userService from "@/lib/userService";

interface DownloadRecord {
  id: number;
  notice_id: string;
  notice_title: string;
  notice_type: string;
  file_name: string;
  file_size: string;
  download_time: string;
  site_name: string;
  file_url: string;
}

export default function DownloadsPage() {
  const [downloads, setDownloads] = useState<DownloadRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [typeFilter, setTypeFilter] = useState("");
  const [timeFilter, setTimeFilter] = useState("");

  useEffect(() => {
    fetchDownloads();
  }, []);

  const fetchDownloads = async () => {
    try {
      setLoading(true);
      const token = userService.getAccessToken();
      if (!token) return;

      // 这里应该调用实际的API
      // const response = await fetch('/api/user/downloads', {
      //   headers: { 'Authorization': `Bearer ${token}` }
      // });
      // const data = await response.json();
      // setDownloads(data.downloads || []);

      // 模拟数据
      setTimeout(() => {
        setDownloads([
          {
            id: 1,
            notice_id: "N2024001",
            notice_title: "某市政府采购办公设备招标公告",
            notice_type: "招标公告",
            file_name: "招标文件.pdf",
            file_size: "2.5MB",
            download_time: "2024-06-20 14:30:00",
            site_name: "政府采购网",
            file_url: "/downloads/tender_doc_001.pdf"
          },
          {
            id: 2,
            notice_id: "N2024002",
            notice_title: "教育局教学设备采购项目",
            notice_type: "中标结果",
            file_name: "中标结果公示.pdf",
            file_size: "1.8MB",
            download_time: "2024-06-19 09:15:00",
            site_name: "教育采购平台",
            file_url: "/downloads/result_002.pdf"
          },
          {
            id: 3,
            notice_id: "N2024003",
            notice_title: "医院医疗设备采购招标",
            notice_type: "招标公告",
            file_name: "技术规格书.doc",
            file_size: "3.2MB",
            download_time: "2024-06-18 16:45:00",
            site_name: "医疗采购网",
            file_url: "/downloads/spec_003.doc"
          }
        ]);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Failed to fetch downloads:', error);
      setLoading(false);
    }
  };

  const handleDelete = async (downloadId: number) => {
    try {
      // 这里应该调用删除API
      setDownloads(prev => prev.filter(item => item.id !== downloadId));
    } catch (error) {
      console.error('Failed to delete download record:', error);
    }
  };

  const handleRedownload = (download: DownloadRecord) => {
    // 创建下载链接
    const link = document.createElement('a');
    link.href = download.file_url;
    link.download = download.file_name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <File className="h-5 w-5 text-red-500" />;
      case 'doc':
      case 'docx':
        return <File className="h-5 w-5 text-blue-500" />;
      case 'xls':
      case 'xlsx':
        return <File className="h-5 w-5 text-green-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-500 dark:text-gray-400" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "招标公告":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";
      case "中标结果":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";
      case "招标计划":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300";
      default:
        return "bg-gray-100 dark:bg-gray-700 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300";
    }
  };

  const filteredDownloads = downloads.filter(download => {
    const matchesSearch = download.notice_title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         download.file_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = !typeFilter || download.notice_type === typeFilter;
    const matchesTime = !timeFilter || (() => {
      const downloadDate = new Date(download.download_time);
      const now = new Date();
      switch (timeFilter) {
        case "today":
          return downloadDate.toDateString() === now.toDateString();
        case "week":
          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          return downloadDate >= weekAgo;
        case "month":
          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          return downloadDate >= monthAgo;
        default:
          return true;
      }
    })();
    
    return matchesSearch && matchesType && matchesTime;
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">我的下载</h1>
          <p className="text-gray-600 dark:text-gray-400">查看和管理您的下载记录</p>
        </div>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"></div>
                <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">我的下载</h1>
        <p className="text-gray-600 dark:text-gray-400">查看和管理您的下载记录</p>
      </div>

      {/* 筛选器 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Input
                placeholder="搜索文件或公告..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="公告类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部类型</SelectItem>
                  <SelectItem value="招标公告">招标公告</SelectItem>
                  <SelectItem value="中标结果">中标结果</SelectItem>
                  <SelectItem value="招标计划">招标计划</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Select value={timeFilter} onValueChange={setTimeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="下载时间" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">全部时间</SelectItem>
                  <SelectItem value="today">今天</SelectItem>
                  <SelectItem value="week">最近一周</SelectItem>
                  <SelectItem value="month">最近一月</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Button 
                onClick={() => {
                  setSearchTerm("");
                  setTypeFilter("");
                  setTimeFilter("");
                }} 
                variant="outline"
                className="w-full"
              >
                清除筛选
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 下载列表 */}
      {filteredDownloads.length > 0 ? (
        <div className="space-y-4">
          {filteredDownloads.map((download) => (
            <Card key={download.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-3">
                    <div className="flex items-center space-x-3">
                      {getFileIcon(download.file_name)}
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">
                          {download.file_name}
                        </h3>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{download.file_size}</p>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">
                        {download.notice_title}
                      </h4>
                      <div className="flex items-center space-x-4 text-sm text-gray-600 dark:text-gray-400">
                        <span className={`px-2 py-1 rounded-full text-xs ${getTypeColor(download.notice_type)}`}>
                          {download.notice_type}
                        </span>
                        <div className="flex items-center">
                          <Building className="h-4 w-4 mr-1" />
                          {download.site_name}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          {new Date(download.download_time).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRedownload(download)}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      重新下载
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(download.id)}
                    >
                      <Trash2 className="h-4 w-4 mr-1" />
                      删除记录
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Download className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              暂无下载记录
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-center">
              {Object.values({searchTerm, typeFilter, timeFilter}).some(v => v) 
                ? '没有找到符合条件的下载记录，尝试调整筛选条件' 
                : '您还没有下载过任何文件'
              }
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
