import Link from "next/link";
import { Home, FileText, User } from "lucide-react";

export default function MobileNavigation() {
  return (
    <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-40">
      <div className="flex justify-around">
        <Link href="/" className="flex flex-col items-center py-2">
          <Home className="h-6 w-6 text-blue-600" />
          <span className="text-xs mt-1 text-gray-600">首页</span>
        </Link>
        <Link href="/notices" className="flex flex-col items-center py-2">
          <FileText className="h-6 w-6 text-gray-600" />
          <span className="text-xs mt-1 text-gray-600">订阅</span>
        </Link>
        <Link href="/profile" className="flex flex-col items-center py-2">
          <User className="h-6 w-6 text-gray-600" />
          <span className="text-xs mt-1 text-gray-600">我的</span>
        </Link>
      </div>
    </div>
  );
}
