import { NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

interface Notice {
  id: number;
  notice_title: string;
  notice_third_type_desc: string;
  province: string;
  publish_date: string;
}

const TABS = [
  { label: "招标计划", type: "招标计划" },
  { label: "招标公告", type: "招标公告" },
  { label: "中标结果", type: "中标结果" },
  { label: "拟在建项目", type: "在建项目" },
];

export async function GET() {
  try {
    const noticesByType: Record<string, Notice[]> = {};
    
    for (const tab of TABS) {
      try {
        const sql = `SELECT id, notice_title, notice_third_type_desc, province, publish_date FROM trading_notices WHERE notice_third_type_desc = ? ORDER BY publish_date DESC, id DESC LIMIT 9`;
        const rows = await executeQuery<Notice[]>(sql, [tab.type]);
        noticesByType[tab.type] = rows;
      } catch (error) {
        console.error(`Failed to fetch notices for ${tab.type}:`, error);
        // 如果某个类型的数据获取失败，设置为空数组
        noticesByType[tab.type] = [];
      }
    }

    return NextResponse.json({
      success: true,
      noticesByType
    });
  } catch (error) {
    console.error('Failed to fetch latest notices:', error);
    
    // 返回空数据而不是错误，以确保前端能正常显示
    const emptyData: Record<string, Notice[]> = {};
    TABS.forEach(tab => {
      emptyData[tab.type] = [];
    });
    
    return NextResponse.json({
      success: false,
      noticesByType: emptyData,
      error: 'Failed to fetch notices'
    });
  }
}
