"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { MessageSquare, Send, Star } from "lucide-react";

interface FeedbackForm {
  type: string;
  title: string;
  content: string;
  contact_info: string;
  rating: number;
  is_anonymous: boolean;
}

export default function NewFeedbackPage() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [form, setForm] = useState<FeedbackForm>({
    type: "suggestion",
    title: "",
    content: "",
    contact_info: "",
    rating: 5,
    is_anonymous: false,
  });

  const feedbackTypes = [
    { value: "suggestion", label: "功能建议", description: "对产品功能的改进建议" },
    { value: "bug", label: "问题反馈", description: "发现的系统问题或错误" },
    { value: "complaint", label: "投诉建议", description: "对服务质量的意见" },
    { value: "praise", label: "表扬建议", description: "对产品或服务的好评" },
    { value: "other", label: "其他", description: "其他类型的反馈" },
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!form.title.trim() || !form.content.trim()) {
      toast({
        title: "提交失败",
        description: "请填写标题和反馈内容",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    
    try {
      const response = await fetch('/api/feedback', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(form),
      });

      if (response.ok) {
        toast({
          title: "提交成功",
          description: "感谢您的反馈，我们会认真处理您的建议！",
        });
        
        // 重置表单
        setForm({
          type: "suggestion",
          title: "",
          content: "",
          contact_info: "",
          rating: 5,
          is_anonymous: false,
        });
      } else {
        throw new Error('提交失败');
      }
    } catch (error) {
      toast({
        title: "提交失败",
        description: "网络错误，请稍后重试",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FeedbackForm, value: any) => {
    setForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <div className="flex items-center mb-4">
          <MessageSquare className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-2" />
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">提交反馈</h1>
        </div>
        <p className="text-gray-600 dark:text-gray-400">
          您的意见对我们很重要，请告诉我们您的想法和建议
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Feedback Form */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>反馈信息</CardTitle>
              <CardDescription>
                请详细描述您的问题或建议，我们会尽快回复您
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Feedback Type */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">反馈类型</Label>
                  <RadioGroup
                    value={form.type}
                    onValueChange={(value) => handleInputChange('type', value)}
                    className="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    {feedbackTypes.map((type) => (
                      <div key={type.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={type.value} id={type.value} />
                        <div className="grid gap-1.5 leading-none">
                          <Label
                            htmlFor={type.value}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                          >
                            {type.label}
                          </Label>
                          <p className="text-xs text-gray-500 dark:text-gray-400">{type.description}</p>
                        </div>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                {/* Title */}
                <div className="space-y-2">
                  <Label htmlFor="title">反馈标题 *</Label>
                  <Input
                    id="title"
                    placeholder="请简要描述您的问题或建议"
                    value={form.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    required
                  />
                </div>

                {/* Content */}
                <div className="space-y-2">
                  <Label htmlFor="content">详细描述 *</Label>
                  <Textarea
                    id="content"
                    placeholder="请详细描述您遇到的问题或具体的建议..."
                    value={form.content}
                    onChange={(e) => handleInputChange('content', e.target.value)}
                    rows={6}
                    required
                  />
                </div>

                {/* Rating */}
                <div className="space-y-3">
                  <Label className="text-base font-medium">整体评分</Label>
                  <div className="flex items-center space-x-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => handleInputChange('rating', star)}
                        className={`p-1 rounded ${
                          star <= form.rating
                            ? 'text-yellow-400'
                            : 'text-gray-300 dark:text-gray-600 dark:text-gray-400'
                        }`}
                      >
                        <Star className="h-6 w-6 fill-current" />
                      </button>
                    ))}
                    <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                      {form.rating} 星
                    </span>
                  </div>
                </div>

                {/* Contact Info */}
                <div className="space-y-2">
                  <Label htmlFor="contact_info">联系方式（可选）</Label>
                  <Input
                    id="contact_info"
                    placeholder="邮箱或手机号，方便我们回复您"
                    value={form.contact_info}
                    onChange={(e) => handleInputChange('contact_info', e.target.value)}
                  />
                </div>

                {/* Anonymous */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="anonymous"
                    checked={form.is_anonymous}
                    onCheckedChange={(checked) => handleInputChange('is_anonymous', checked)}
                  />
                  <Label htmlFor="anonymous" className="text-sm">
                    匿名提交（不显示个人信息）
                  </Label>
                </div>

                {/* Submit Button */}
                <Button type="submit" disabled={loading} className="w-full">
                  {loading ? (
                    "提交中..."
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      提交反馈
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Contact Info */}
          <Card>
            <CardHeader>
              <CardTitle>联系我们</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">客服邮箱</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400"><EMAIL></p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">客服电话</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">400-123-4567</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">工作时间</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  周一至周五 9:00-18:00
                </p>
              </div>
            </CardContent>
          </Card>

          {/* FAQ */}
          <Card>
            <CardHeader>
              <CardTitle>常见问题</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                  如何创建订阅？
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  登录后进入个人中心，点击新建订阅即可
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                  推送不及时怎么办？
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  请检查推送设置，确保开启了相应的推送方式
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                  如何升级会员？
                </h4>
                <p className="text-xs text-gray-600 dark:text-gray-400">
                  进入会员权益页面，选择合适的会员套餐
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
