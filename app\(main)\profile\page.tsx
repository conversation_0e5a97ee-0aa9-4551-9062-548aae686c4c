"use client";

import { useState, useEffect } from "react";
import userService, { type UserData } from "@/lib/userService";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  User,
  Crown,
  Bell,
  BookmarkCheck,
  Settings,
  TrendingUp,
  Calendar,
  Mail,
  Smartphone
} from "lucide-react";

interface Subscription {
  id: number;
  name: string;
  keywords: string[];
  regions: string[];
  subscription_type: string;
  is_active: boolean;
  match_count: number;
  created_at: string;
}

interface PushRecord {
  id: number;
  push_title: string;
  push_type: string;
  push_status: string;
  push_time: string;
  subscription_name?: string;
}

interface UserStats {
  total_pushes: number;
  sent_count: number;
  delivered_count: number;
  failed_count: number;
  wechat_count: number;
  email_count: number;
}

export default function ProfilePage() {
  const router = useRouter();
  const [user, setUser] = useState<UserData | null>(null);
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [recentPushes, setRecentPushes] = useState<PushRecord[]>([]);
  const [userStats, setUserStats] = useState<UserStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadUserData = async () => {
      if (userService.isLoggedIn()) {
        const currentUser = userService.getCurrentUser();
        setUser(currentUser);

        // Load user subscriptions and push records
        await Promise.all([
          loadSubscriptions(),
          loadPushRecords(),
          loadUserStats()
        ]);
      } else {
        router.push("/login");
      }
      setIsLoading(false);
    };

    loadUserData();
  }, []);

  const loadSubscriptions = async () => {
    try {
      const response = await fetch('/api/subscriptions', {
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setSubscriptions(data.data);
      }
    } catch (error) {
      console.error('Failed to load subscriptions:', error);
    }
  };

  const loadPushRecords = async () => {
    try {
      const response = await fetch('/api/user/push-records?limit=5', {
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setRecentPushes(data.data.records);
      }
    } catch (error) {
      console.error('Failed to load push records:', error);
    }
  };

  const loadUserStats = async () => {
    try {
      const response = await fetch('/api/user/push-records', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'statistics', days: 30 })
      });
      const data = await response.json();
      if (data.success) {
        setUserStats(data.data.summary);
      }
    } catch (error) {
      console.error('Failed to load user stats:', error);
    }
  };

  const getMembershipBadge = (membershipType: string) => {
    switch (membershipType) {
      case 'vip':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"><Crown className="w-3 h-3 mr-1" />VIP会员</span>;
      case 'premium':
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800"><Crown className="w-3 h-3 mr-1" />高级会员</span>;
      default:
        return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200">免费会员</span>;
    }
  };

  const getPushTypeIcon = (pushType: string) => {
    switch (pushType) {
      case 'email':
        return <Mail className="w-4 h-4" />;
      case 'sms':
        return <Smartphone className="w-4 h-4" />;
      case 'wechat':
        return <Bell className="w-4 h-4" />;
      default:
        return <Bell className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return 'text-green-600 dark:text-green-400';
      case 'failed':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-yellow-600 dark:text-yellow-400';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {user && (
          <div className="space-y-6">
            {/* User Profile Header */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                      <User className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <h1 className="text-2xl font-bold text-gray-900 dark:text-white">{user.contact_name}</h1>
                      <p className="text-gray-600 dark:text-gray-400">{user.company_name}</p>
                      <div className="mt-2">
                        {getMembershipBadge((user as any).membership_type || 'free')}
                      </div>
                    </div>
                  </div>
                  <div className="flex space-x-3">
                    <Link
                      href="/profile/settings"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      设置
                    </Link>
                    <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600">
                      <Crown className="w-4 h-4 mr-2" />
                      升级VIP会员
                    </button>
                  </div>
                </div>
              </div>

              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">手机号码</label>
                    <p className="mt-1 text-gray-900 dark:text-white">{user.phone}</p>
                  </div>
                  {user.email && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">邮箱地址</label>
                      <p className="mt-1 text-gray-900 dark:text-white">{user.email}</p>
                    </div>
                  )}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">注册时间</label>
                    <p className="mt-1 text-gray-900 dark:text-white">
                      {new Date(user.created_at).toLocaleDateString("zh-CN")}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Statistics Cards */}
            {userStats && (
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <TrendingUp className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">总推送数</dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">{userStats.total_pushes}</dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <BookmarkCheck className="h-6 w-6 text-green-600 dark:text-green-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">成功推送</dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">{userStats.delivered_count}</dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Bell className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">微信推送</dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">{userStats.wechat_count}</dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <Mail className="h-6 w-6 text-purple-600" />
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <dl>
                          <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">邮件推送</dt>
                          <dd className="text-lg font-medium text-gray-900 dark:text-white">{userStats.email_count}</dd>
                        </dl>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* My Subscriptions */}
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">我的订阅</h2>
                    <Link
                      href="/profile/subscriptions"
                      className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500"
                    >
                      查看更多 →
                    </Link>
                  </div>
                </div>
                <div className="p-6">
                  {subscriptions.length > 0 ? (
                    <div className="space-y-4">
                      {subscriptions.slice(0, 3).map((subscription) => (
                        <div key={subscription.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="text-sm font-medium text-gray-900 dark:text-white">{subscription.name}</h3>
                              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                关键词: {subscription.keywords.slice(0, 3).join(', ')}
                                {subscription.keywords.length > 3 && '...'}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                地区: {subscription.regions.slice(0, 2).join(', ')}
                                {subscription.regions.length > 2 && '...'}
                              </p>
                            </div>
                            <div className="text-right">
                              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                                subscription.is_active
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200'
                              }`}>
                                {subscription.is_active ? '活跃' : '暂停'}
                              </div>
                              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                匹配: {subscription.match_count}
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                      {subscriptions.length === 0 && (
                        <div className="text-center py-8">
                          <BookmarkCheck className="mx-auto h-12 w-12 text-gray-400" />
                          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无订阅</h3>
                          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">创建您的第一个订阅来接收推送通知</p>
                          <div className="mt-6">
                            <Link
                              href="/profile/subscriptions/new"
                              className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                            >
                              创建订阅
                            </Link>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <BookmarkCheck className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无订阅</h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">创建您的第一个订阅来接收推送通知</p>
                      <div className="mt-6">
                        <Link
                          href="/profile/subscriptions/new"
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                        >
                          创建订阅
                        </Link>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Recent Push Records */}
              <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex items-center justify-between">
                    <h2 className="text-lg font-medium text-gray-900 dark:text-white">最近推送记录</h2>
                    <Link
                      href="/profile/push-records"
                      className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500"
                    >
                      查看更多 →
                    </Link>
                  </div>
                </div>
                <div className="p-6">
                  {recentPushes.length > 0 ? (
                    <div className="space-y-4">
                      {recentPushes.map((push) => (
                        <div key={push.id} className="flex items-start space-x-3">
                          <div className="flex-shrink-0 mt-1">
                            {getPushTypeIcon(push.push_type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                              {push.push_title}
                            </p>
                            {push.subscription_name && (
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                来自订阅: {push.subscription_name}
                              </p>
                            )}
                            <div className="flex items-center justify-between mt-1">
                              <p className="text-xs text-gray-500 dark:text-gray-400">
                                {new Date(push.push_time).toLocaleString('zh-CN')}
                              </p>
                              <span className={`text-xs font-medium ${getStatusColor(push.push_status)}`}>
                                {push.push_status === 'sent' ? '已发送' :
                                 push.push_status === 'delivered' ? '已送达' :
                                 push.push_status === 'failed' ? '发送失败' : '待发送'}
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Bell className="mx-auto h-12 w-12 text-gray-400" />
                      <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无推送记录</h3>
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">创建订阅后将在这里显示推送记录</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
  );
}
