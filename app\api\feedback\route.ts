import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import userService from '@/lib/userService';



// POST - 提交反馈
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, title, content, contact_info, rating, is_anonymous } = body;

    // 验证必填字段
    if (!type || !title || !content) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 获取用户信息（如果已登录）
    const authHeader = request.headers.get('authorization');
    const user_id = null;

    if (authHeader) {
      try {
        // 这里应该实现JWT token验证逻辑
        // 暂时跳过验证，允许匿名提交
        // const token = authHeader.replace('Bearer ', '');
        // const userData = jwt.verify(token, process.env.JWT_SECRET);
        // user_id = userData.id;
      } catch (error) {
        // 忽略token验证错误，允许匿名提交
      }
    }

    // 插入反馈记录
    const insertQuery = `
      INSERT INTO user_feedback (
        user_id, type, title, content, contact_info, rating, 
        is_anonymous, status, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())
    `;

    const result = await executeQuery(insertQuery, [
      user_id,
      type,
      title,
      content,
      contact_info || null,
      rating || 5,
      is_anonymous || false
    ]);

    return NextResponse.json({
      message: 'Feedback submitted successfully',
      id: (result as any).insertId
    });
  } catch (error) {
    console.error('Failed to submit feedback:', error);
    return NextResponse.json(
      { error: 'Failed to submit feedback' },
      { status: 500 }
    );
  }
}


