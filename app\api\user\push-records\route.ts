import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

// Types for push records
interface PushRecord {
  id: number;
  user_id: number;
  subscription_id: number | null;
  notice_id: string | null;
  push_type: 'wechat' | 'email' | 'sms' | 'app';
  push_content: string;
  push_title: string;
  push_status: 'pending' | 'sent' | 'delivered' | 'failed';
  push_time: string;
  delivery_time: string | null;
  error_message: string | null;
  created_at: string;
  updated_at: string;
  subscription_name?: string;
  notice_title?: string;
}

// Helper function to verify JWT token and get user ID
async function getUserFromToken(request: NextRequest): Promise<number | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    return decoded.user_id || decoded.id;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// GET - Fetch user's push records
export async function GET(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const push_type = searchParams.get('push_type');
    const push_status = searchParams.get('push_status');
    const days = parseInt(searchParams.get('days') || '30'); // Default to last 30 days

    const offset = (page - 1) * limit;

    // Build query with filters
    let whereClause = 'WHERE pr.user_id = ? AND pr.push_time >= DATE_SUB(NOW(), INTERVAL ? DAY)';
    const params: any[] = [userId, days];

    if (push_type) {
      whereClause += ' AND pr.push_type = ?';
      params.push(push_type);
    }

    if (push_status) {
      whereClause += ' AND pr.push_status = ?';
      params.push(push_status);
    }

    // Get push records with related information
    const records = await executeQuery<PushRecord[]>(
      `SELECT 
        pr.id, pr.user_id, pr.subscription_id, pr.notice_id,
        pr.push_type, pr.push_content, pr.push_title, pr.push_status,
        pr.push_time, pr.delivery_time, pr.error_message,
        pr.created_at, pr.updated_at,
        us.name as subscription_name,
        tn.notice_title
      FROM user_push_records pr
      LEFT JOIN user_subscriptions us ON pr.subscription_id = us.id
      LEFT JOIN trading_notices tn ON pr.notice_id = tn.notice_id
      ${whereClause}
      ORDER BY pr.push_time DESC
      LIMIT ? OFFSET ?`,
      [...params, limit, offset]
    );

    // Get total count for pagination
    const totalResult = await executeQuery<any[]>(
      `SELECT COUNT(*) as total
      FROM user_push_records pr
      ${whereClause}`,
      params
    );

    const total = totalResult[0].total;
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        records,
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Error fetching push records:', error);
    return NextResponse.json(
      { error: 'Failed to fetch push records' },
      { status: 500 }
    );
  }
}

// GET - Get push statistics
export async function POST(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { action } = body;

    if (action === 'statistics') {
      const days = body.days || 30;

      // Get push statistics
      const stats = await executeQuery<any[]>(
        `SELECT 
          push_type,
          push_status,
          COUNT(*) as count,
          DATE(push_time) as push_date
        FROM user_push_records 
        WHERE user_id = ? AND push_time >= DATE_SUB(NOW(), INTERVAL ? DAY)
        GROUP BY push_type, push_status, DATE(push_time)
        ORDER BY push_date DESC`,
        [userId, days]
      );

      // Get summary statistics
      const summary = await executeQuery<any[]>(
        `SELECT 
          COUNT(*) as total_pushes,
          SUM(CASE WHEN push_status = 'sent' THEN 1 ELSE 0 END) as sent_count,
          SUM(CASE WHEN push_status = 'delivered' THEN 1 ELSE 0 END) as delivered_count,
          SUM(CASE WHEN push_status = 'failed' THEN 1 ELSE 0 END) as failed_count,
          SUM(CASE WHEN push_type = 'wechat' THEN 1 ELSE 0 END) as wechat_count,
          SUM(CASE WHEN push_type = 'email' THEN 1 ELSE 0 END) as email_count,
          SUM(CASE WHEN push_type = 'sms' THEN 1 ELSE 0 END) as sms_count
        FROM user_push_records 
        WHERE user_id = ? AND push_time >= DATE_SUB(NOW(), INTERVAL ? DAY)`,
        [userId, days]
      );

      // Get recent push records (last 10)
      const recentRecords = await executeQuery<any[]>(
        `SELECT 
          pr.push_title, pr.push_type, pr.push_status, pr.push_time,
          us.name as subscription_name
        FROM user_push_records pr
        LEFT JOIN user_subscriptions us ON pr.subscription_id = us.id
        WHERE pr.user_id = ?
        ORDER BY pr.push_time DESC
        LIMIT 10`,
        [userId]
      );

      return NextResponse.json({
        success: true,
        data: {
          statistics: stats,
          summary: summary[0] || {
            total_pushes: 0,
            sent_count: 0,
            delivered_count: 0,
            failed_count: 0,
            wechat_count: 0,
            email_count: 0,
            sms_count: 0
          },
          recentRecords
        }
      });
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error processing push records request:', error);
    return NextResponse.json(
      { error: 'Failed to process request' },
      { status: 500 }
    );
  }
}

// DELETE - Clear old push records
export async function DELETE(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const days = parseInt(searchParams.get('days') || '90'); // Default to clear records older than 90 days

    // Delete old push records
    const result = await executeQuery(
      `DELETE FROM user_push_records 
       WHERE user_id = ? AND push_time < DATE_SUB(NOW(), INTERVAL ? DAY)`,
      [userId, days]
    );

    return NextResponse.json({
      success: true,
      message: `Cleared push records older than ${days} days`,
      data: { deletedCount: (result as any).affectedRows }
    });
  } catch (error) {
    console.error('Error clearing push records:', error);
    return NextResponse.json(
      { error: 'Failed to clear push records' },
      { status: 500 }
    );
  }
}
