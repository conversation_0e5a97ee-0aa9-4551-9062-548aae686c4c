import Link from "next/link";
import Image from "next/image";

interface HotTopic {
  title: string;
  seed: number; // 为Picsum添加随机种子
  href: string;
}

const topics: HotTopic[] = [
  {
    title: "国网",
    seed: 123,
    href: "/topic/national-grid",
  },
  {
    title: "铁路",
    seed: 456,
    href: "/topic/railway",
  },
  {
    title: "家具",
    seed: 789,
    href: "/topic/furniture",
  },
  {
    title: "电力",
    seed: 101,
    href: "/topic/electric",
  },
];

export default function HotTopics() {
  return (
    <div className="my-10">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">热门专题</h2>
        <Link href="/topics" className="text-sm text-blue-600 dark:text-blue-400 hover:underline">
          查看更多
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {topics.map((topic) => (
          <Link 
            key={topic.title} 
            href={topic.href}
            className="relative overflow-hidden rounded-lg block group h-28"
          >
            {/* 背景图片 - 使用Picsum Photos */}
            <div className="absolute inset-0 bg-black/20 dark:bg-black/40 z-10 group-hover:bg-black/30 dark:group-hover:bg-black/50 transition-colors"></div>
            <img 
              src={`https://picsum.photos/seed/${topic.seed}/300/150`}
              alt={topic.title}
              className="absolute inset-0 object-cover w-full h-full transition-transform duration-500 group-hover:scale-110"
            />
            
            {/* 专题标题 */}
            <div className="absolute left-6 bottom-5 z-20 text-white font-medium text-xl flex items-center">
              <span>{topic.title}</span>
              <div className="ml-3 opacity-0 group-hover:opacity-100 transition-opacity transform translate-x-0 group-hover:translate-x-1 duration-300">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
