import Image from "next/image";
import Link from "next/link";

export default function StatisticsSection() {
  return (
    <div className="py-8 bg-white dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-xl font-semibold mb-8 text-center text-gray-900 dark:text-gray-100">
          招标通您身边的投标顾问
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <Link
            href="/statistics"
            className="flex flex-col items-center text-center hover:opacity-90 transition-opacity"
          >
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full w-20 h-20 mb-4 flex items-center justify-center overflow-hidden">
              <Image
                src="https://picsum.photos/64/64"
                width={64}
                height={64}
                alt="Statistics"
                className="w-full h-full object-cover rounded-full"
              />
            </div>
            <h3 className="text-gray-900 dark:text-gray-100 font-medium mb-1">
              海量数据
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              覆盖全国31省市
            </p>
          </Link>

          <Link
            href="/projects"
            className="flex flex-col items-center text-center hover:opacity-90 transition-opacity"
          >
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full w-20 h-20 mb-4 flex items-center justify-center overflow-hidden">
              <Image
                src="https://picsum.photos/64/64"
                width={64}
                height={64}
                alt="Projects"
                className="w-full h-full object-cover rounded-full"
              />
            </div>
            <h3 className="text-gray-900 dark:text-gray-100 font-medium mb-1">
              精准项目
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              海量行业项目
            </p>
          </Link>

          <Link
            href="/services"
            className="flex flex-col items-center text-center hover:opacity-90 transition-opacity"
          >
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full w-20 h-20 mb-4 flex items-center justify-center overflow-hidden">
              <Image
                src="https://picsum.photos/64/64"
                width={64}
                height={64}
                alt="Services"
                className="w-full h-full object-cover rounded-full"
              />
            </div>
            <h3 className="text-gray-900 dark:text-gray-100 font-medium mb-1">
              专业服务
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              全流程投标支持
            </p>
          </Link>

          <Link
            href="/support"
            className="flex flex-col items-center text-center hover:opacity-90 transition-opacity"
          >
            <div className="bg-gray-100 dark:bg-gray-800 rounded-full w-20 h-20 mb-4 flex items-center justify-center overflow-hidden">
              <Image
                src="https://picsum.photos/64/64"
                width={64}
                height={64}
                alt="Support"
                className="w-full h-full object-cover rounded-full"
              />
            </div>
            <h3 className="text-gray-900 dark:text-gray-100 font-medium mb-1">
              技术支持
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              7*24小时服务
            </p>
          </Link>
        </div>
      </div>
    </div>
  );
}
