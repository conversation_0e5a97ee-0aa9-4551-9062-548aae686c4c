"use client";

import { useEffect, useState } from "react";
import SearchBar from "./SearchBar";

const hotTags = [
  "交通",
  "广告",
  "绿化",
  "监控",
  "物业",
  "工程",
  "装修",
  "软件",
];

export default function HeroSection() {
  return (
    <div className="relative bg-gradient-to-r from-blue-500 to-blue-600 py-12 md:py-24 overflow-hidden">
      {/* Grid Overlay */}
      <div 
        className="absolute inset-0 z-0"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255, 255, 255, 0.07) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 255, 255, 0.07) 1px, transparent 1px)
          `,
          backgroundSize: '40px 40px',
          maskImage: 'radial-gradient(ellipse at center, black 60%, transparent 100%)',
          WebkitMaskImage: 'radial-gradient(ellipse at center, black 60%, transparent 100%)', // For Safari compatibility
        }}
      ></div>
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8">
          <h1 className="text-2xl md:text-4xl font-bold text-white mb-4">
            招标通您身边的投标顾问
          </h1>
        </div>

        <SearchBar />

        <div className="mt-4 text-center text-sm text-white">
          <span className="mr-2">热搜关键词:</span>
          {hotTags.map((tag, index) => (
            <span
              key={tag}
              className="inline-block mx-1 hover:text-blue-200 cursor-pointer"
            >
              {tag}
              {index < hotTags.length - 1 && <span className="ml-1"></span>}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}
