import { S<PERSON><PERSON>_NAME, host } from "@/data";
import { executeQuery } from "@/lib/db";
import { Metadata, ResolvingMetadata } from "next";
import { notFound } from "next/navigation";
import PlanDetail from "./PlanDetail";
import {
  Bidding<PERSON>lan,
  BiddingProject,
  NoticeDetail,
  TenderFile,
} from "@/lib/type";

// Function to format date from YYYYMMDD to YYYY-MM-DD
function formatDate(dateStr: string | null): string {
  if (!dateStr) return "";

  // Handle YYYYMMDD format
  if (dateStr.length === 8) {
    const year = dateStr.substring(0, 4);
    const month = dateStr.substring(4, 6);
    const day = dateStr.substring(6, 8);
    return `${year}-${month}-${day}`;
  }

  // Handle other formats or return as is if can't parse
  return dateStr;
}

// Function to get notice data by ID
async function getNoticeDetail(id: string): Promise<NoticeDetail | null> {
  try {
    const results = await executeQuery<NoticeDetail[]>(
      `SELECT * FROM trading_notices WHERE id = ?`,
      [id]
    );

    return results.length > 0 ? results[0] : null;
  } catch (error) {
    console.error("Error fetching notice detail:", error);
    return null;
  }
}

// Function to get bidding plans by notice_id
async function getBiddingPlans(noticeId: string): Promise<BiddingPlan[]> {
  try {
    const results = await executeQuery<BiddingPlan[]>(
      `SELECT * FROM bidding_plans WHERE notice_id = ?`,
      [noticeId]
    );

    return results;
  } catch (error) {
    console.error("Error fetching bidding plans:", error);
    return [];
  }
}

// Function to get bidding projects by plan_row_guid
async function getBiddingProjects(
  planRowGuid: string
): Promise<BiddingProject[]> {
  try {
    const results = await executeQuery<BiddingProject[]>(
      `SELECT * FROM bidding_projects WHERE plan_row_guid = ?`,
      [planRowGuid]
    );

    return results;
  } catch (error) {
    console.error("Error fetching bidding projects:", error);
    return [];
  }
}

// Function to get tender files by row_guid
async function getTenderFiles(rowGuid: string): Promise<TenderFile[]> {
  try {
    const results = await executeQuery<TenderFile[]>(
      `SELECT * FROM tender_files WHERE row_guid = ?`,
      [rowGuid]
    );

    return results;
  } catch (error) {
    console.error("Error fetching tender files:", error);
    return [];
  }
}

export const revalidate = 60;

export async function generateMetadata(
  { params }: { params: Promise<{ id?: string }> },
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { id } = await params;

  if (!id) {
    return {
      title: `招标计划 - ${SITE_NAME}`,
    };
  }

  const notice = await getNoticeDetail(id);

  if (!notice) {
    return {
      title: `招标计划 - ${SITE_NAME}`,
    };
  }

  return {
    title: `${notice.notice_title} - 招标计划 - ${SITE_NAME}`,
    description: notice.content?.substring(0, 150) || "",
    openGraph: {
      title: `${notice.notice_title} - 招标计划 - ${SITE_NAME}`,
      description: notice.content?.substring(0, 150) || "",
      url: `${host}/notices/plan/${id}`,
      siteName: SITE_NAME,
      locale: "zh_CN",
      type: "article",
    },
  };
}

// Server component for data fetching
export default async function PlanDetailPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  if (!id || isNaN(parseInt(id))) {
    return notFound();
  }

  // Get notice details
  const notice = await getNoticeDetail(id);

  if (!notice) {
    return notFound();
  }

  // Get notice ID from the notice
  const noticeId = notice.notice_id;

  // First, fetch plans data
  const plans = await getBiddingPlans(noticeId);

  // If no plans are found but notice exists, we'll still show the notice

  if (!plans || plans.length == 0) {
    return notFound();
  }

  // 获取所有计划关联的项目
  let allProjects: BiddingProject[] = [];

  // 为每个计划获取关联的项目
  if (plans.length > 0) {
    for (const plan of plans) {
      if (plan.row_guid) {
        const planProjects = await getBiddingProjects(plan.row_guid);
        allProjects = [...allProjects, ...planProjects];
      }
    }
  }

  // Get files for all plans
  let files: TenderFile[] = [];
  if (plans.length > 0) {
    for (const plan of plans) {
      if (plan.row_guid) {
        const planFiles = await getTenderFiles(plan.row_guid);
        files = [...files, ...planFiles];
      }
    }
  }

  // Format dates for display
  const formattedPublishDate = formatDate(notice.publish_date);

  // Get formatted dates from the first plan if available
  let formattedSubmitTime = "";
  let formattedCreateTime = "";
  let formattedSendTime = "";

  if (plans.length > 0) {
    formattedSubmitTime = plans[0].submit_time
      ? formatDate(plans[0].submit_time)
      : "";
    formattedCreateTime = plans[0].create_time
      ? formatDate(plans[0].create_time)
      : "";
    formattedSendTime = plans[0].send_time
      ? formatDate(plans[0].send_time)
      : "";
  }

  // Render the client component with all the fetched data
  return (
    <PlanDetail
      notice={notice}
      plans={plans}
      projects={allProjects}
      files={files}
      formattedPublishDate={formattedPublishDate}
      formattedSubmitTime={formattedSubmitTime}
      formattedCreateTime={formattedCreateTime}
      formattedSendTime={formattedSendTime}
    />
  );
}
