"use client";

import { useEffect } from "react";

export default function PlanDetailPrintStyles() {
  useEffect(() => {
    // 添加专门的打印样式
    const printStyles = document.createElement('style');
    printStyles.id = 'plan-detail-print-styles';
    printStyles.innerHTML = `
      @media print {
        /* 基础打印样式重置 */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }
        
        /* 页面设置 */
        @page {
          size: A4;
          margin: 1.5cm;
          @top-center {
            content: "招标计划详情";
            font-size: 14pt;
            font-weight: bold;
            margin-bottom: 1cm;
          }
          @bottom-center {
            content: "第 " counter(page) " 页";
            font-size: 10pt;
            color: #666;
          }
          @bottom-right {
            content: "打印时间: " attr(data-print-time);
            font-size: 8pt;
            color: #666;
          }
        }
        
        /* 隐藏不需要打印的元素 */
        .no-print,
        .print-button,
        nav,
        header,
        footer,
        button:not(.print-keep),
        .sidebar,
        .navigation,
        .hover\\:bg-gray-50,
        .hover\\:bg-muted\\/50,
        .md\\:hidden,
        .hidden.md\\:flex {
          display: none !important;
        }
        
        /* 基础布局 */
        body {
          font-family: "SimSun", "宋体", serif !important;
          font-size: 12pt !important;
          line-height: 1.5 !important;
          color: #000 !important;
          background: #fff !important;
          margin: 0 !important;
          padding: 0 !important;
        }
        
        /* 容器样式 */
        .max-w-7xl {
          max-width: none !important;
          margin: 0 !important;
          padding: 0 !important;
        }
        
        /* 标题样式 */
        h1 {
          font-size: 18pt !important;
          font-weight: bold !important;
          text-align: center !important;
          margin: 0 0 20pt 0 !important;
          page-break-after: avoid !important;
          border-bottom: 2pt solid #000 !important;
          padding-bottom: 10pt !important;
        }
        
        h2 {
          font-size: 14pt !important;
          font-weight: bold !important;
          margin: 15pt 0 10pt 0 !important;
          page-break-after: avoid !important;
          border-bottom: 1pt solid #666 !important;
          padding-bottom: 5pt !important;
        }
        
        h3 {
          font-size: 12pt !important;
          font-weight: bold !important;
          margin: 10pt 0 5pt 0 !important;
          page-break-after: avoid !important;
        }
        
        /* 卡片和容器 */
        .bg-white,
        .bg-gray-800,
        .shadow,
        .border,
        .rounded-lg {
          background: transparent !important;
          box-shadow: none !important;
          border: 1pt solid #000 !important;
          border-radius: 0 !important;
          margin-bottom: 15pt !important;
        }
        
        /* PDF导出区域 */
        .pdf-export-area {
          background: white !important;
          color: black !important;
          border: none !important;
          box-shadow: none !important;
          margin: 0 !important;
          padding: 0 !important;
        }
        
        /* 避免分页的元素 */
        .avoid-break {
          page-break-inside: avoid !important;
          break-inside: avoid !important;
        }
        
        /* 表格样式 */
        table {
          width: 100% !important;
          border-collapse: collapse !important;
          margin: 10pt 0 !important;
          font-size: 11pt !important;
        }
        
        table, th, td {
          border: 1pt solid #000 !important;
        }
        
        th {
          background-color: #f0f0f0 !important;
          font-weight: bold !important;
          padding: 8pt !important;
          text-align: center !important;
        }
        
        td {
          padding: 6pt 8pt !important;
          vertical-align: top !important;
        }
        
        /* 描述列表样式 */
        dl {
          margin: 0 !important;
        }
        
        dt {
          font-weight: bold !important;
          margin-top: 8pt !important;
          margin-bottom: 2pt !important;
          font-size: 11pt !important;
        }
        
        dd {
          margin-left: 0 !important;
          margin-bottom: 8pt !important;
          padding-left: 20pt !important;
          border-bottom: 0.5pt dotted #ccc !important;
          padding-bottom: 4pt !important;
        }
        
        /* 网格布局转换为线性布局 */
        .grid {
          display: block !important;
        }
        
        .grid-cols-1,
        .grid-cols-2,
        .grid-cols-3,
        .sm\\:grid-cols-2,
        .sm\\:grid-cols-3 {
          display: block !important;
        }
        
        .col-span-1,
        .col-span-2,
        .col-span-3,
        .sm\\:col-span-1,
        .sm\\:col-span-2 {
          display: block !important;
          width: 100% !important;
        }
        
        /* 文件列表样式 */
        .file-list {
          margin: 10pt 0 !important;
        }
        
        .file-item {
          border-bottom: 0.5pt solid #ccc !important;
          padding: 8pt 0 !important;
          margin-bottom: 5pt !important;
        }
        
        .file-item:last-child {
          border-bottom: none !important;
        }
        
        /* 链接样式 */
        a {
          color: #000 !important;
          text-decoration: none !important;
        }
        
        a[href]:after {
          content: " (" attr(href) ")" !important;
          font-size: 9pt !important;
          color: #666 !important;
          word-break: break-all !important;
        }
        
        /* 文本样式 */
        .text-gray-500,
        .text-gray-600,
        .text-muted-foreground {
          color: #666 !important;
        }
        
        .text-gray-900,
        .text-foreground {
          color: #000 !important;
        }
        
        /* 空白处理 */
        .whitespace-pre-line {
          white-space: pre-line !important;
        }
        
        /* 截断文本在打印时显示完整 */
        .truncate {
          white-space: normal !important;
          overflow: visible !important;
          text-overflow: clip !important;
        }
        
        /* 响应式隐藏在打印时显示 */
        .hidden {
          display: block !important;
        }
        
        /* Flex布局转换 */
        .flex {
          display: block !important;
        }
        
        .flex-1 {
          width: 100% !important;
        }
        
        /* 间距调整 */
        .space-x-4 > * + * {
          margin-left: 0 !important;
          margin-top: 5pt !important;
        }
        
        .space-y-4 > * + * {
          margin-top: 10pt !important;
        }
        
        /* 标签和徽章 */
        .bg-green-100,
        .bg-blue-100,
        .bg-yellow-100 {
          background: #f0f0f0 !important;
          border: 1pt solid #ccc !important;
          padding: 2pt 4pt !important;
        }
        
        .text-green-800,
        .text-blue-800,
        .text-yellow-800 {
          color: #000 !important;
        }
        
        /* 选项卡内容 */
        .tab-content {
          page-break-inside: avoid !important;
        }
        
        /* 深色模式重置 */
        .dark * {
          color: #000 !important;
          background: #fff !important;
          border-color: #000 !important;
        }
        
        /* 确保重要内容不被截断 */
        .important-content {
          page-break-inside: avoid !important;
          orphans: 3 !important;
          widows: 3 !important;
        }
        
        /* 移动端头部隐藏 */
        .md\\:hidden {
          display: none !important;
        }
        
        /* 桌面端头部显示 */
        .hidden.md\\:block {
          display: block !important;
        }
      }
    `;
    
    document.head.appendChild(printStyles);
    
    // 清理函数
    return () => {
      const existingStyles = document.getElementById('plan-detail-print-styles');
      if (existingStyles) {
        document.head.removeChild(existingStyles);
      }
    };
  }, []);
  
  return null;
}
