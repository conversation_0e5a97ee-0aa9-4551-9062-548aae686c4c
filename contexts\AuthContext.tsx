"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import userService, { type UserData } from "@/lib/userService";

interface AuthContextType {
  user: UserData | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (phone: string, password: string) => Promise<void>;
  logout: () => void;
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化时检查用户登录状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        if (userService.isLoggedIn()) {
          const currentUser = userService.getCurrentUser();
          if (currentUser) {
            setUser(currentUser);
          } else {
            // 尝试从服务器获取用户信息
            await refreshUserData();
          }
        }
      } catch (error) {
        console.error("初始化认证状态失败:", error);
        userService.logout();
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (phone: string, password: string) => {
    const result = await userService.login(phone, password);
    if (result.success && result.data) {
      setUser(result.data.user);
    } else {
      throw new Error(result.message || "登录失败");
    }
  };

  const logout = () => {
    userService.logout();
    setUser(null);
  };

  const refreshUserData = async () => {
    try {
      const response = await userService.getProfile();
      if (response.success && response.data) {
        setUser(response.data);
        // 更新localStorage中的用户信息
        if (typeof window !== "undefined") {
          localStorage.setItem("user_info", JSON.stringify(response.data));
        }
      }
    } catch (error) {
      console.error("刷新用户数据失败:", error);
      logout();
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    refreshUserData,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
