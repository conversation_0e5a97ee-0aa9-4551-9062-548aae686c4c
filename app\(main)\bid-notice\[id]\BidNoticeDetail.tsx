"use client";

import { ChevronLeft } from "lucide-react";
import { useState } from "react";
import { File } from "lucide-react";
import Link from "next/link";

// Types for bid notice data
export interface BidNoticeDetail {
  id: number;
  row_guid: string;
  notice_id: string;
  tender_project_name?: string;
  tender_project_code?: string;
  bid_section_names?: string;
  bid_section_codes?: string;
  tenderer_name?: string;
  tenderer_code?: string;
  tenderer_address?: string;
  tenderer_contactor?: string;
  tender_agency_name?: string;
  tender_agency_code?: string;
  tender_agency_address?: string;
  tender_agency_contactor?: string;
  notice_name?: string;
  notice_content?: string;
  notice_send_time?: string;
  notice_media?: string;
  notice_nature?: string;
  notice_type?: string;
  doc_get_start_time?: string;
  doc_get_end_time?: string;
  bid_doc_refer_end_time?: string;
  bid_open_time?: string;
  tender_doc_get_method?: string;
  bid_doc_refer_method?: string;
  bid_open_address?: string;
  trade_plat?: string;
  tender_project_address?: string;
  fund_source?: string;
  project_scale?: string;
  bidding_contents?: string;
  controlled_price?: number;
  price_unit?: string;
  time_limit?: string;
  bid_qual?: string;
  bid_performance_require?: string;
  is_electronic_bid?: string;
  bid_open_method?: string;
  url?: string;
  created_at?: string;
  updated_at?: string;
}

export interface TenderFile {
  id: number;
  attach_guid: string;
  row_guid: string;
  file_name: string;
  url: string;
  file_type: string;
  type_name: string;
}

// Client component for bid notice detail
export default function BidNoticeDetail({
  bidNotice,
  files,
  formattedNoticeTime,
  formattedOpenTime,
  formattedDocGetStartTime,
  formattedDocGetEndTime,
  formattedReferEndTime,
}: {
  bidNotice: BidNoticeDetail;
  files: TenderFile[];
  formattedNoticeTime: string;
  formattedOpenTime: string;
  formattedDocGetStartTime: string;
  formattedDocGetEndTime: string;
  formattedReferEndTime: string;
}) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Desktop Header */}
      <div className="hidden md:block bg-white dark:bg-gray-800 border-b">
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            招标公告详情
          </h1>
        </div>
      </div>

      {/* Mobile Header */}
      <div className="md:hidden bg-blue-500 text-white p-4 flex items-center">
        <Link href="/notices/1" className="mr-2">
          <ChevronLeft className="h-6 w-6" />
        </Link>
        <h1 className="text-xl font-medium text-center flex-1">招标公告详情</h1>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          {/* 详情页标题部分 */}
          <div className="px-4 py-6 sm:px-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-start">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {bidNotice.notice_name || bidNotice.tender_project_name}
              </h2>
              <div className="hidden md:flex space-x-2">
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
                    />
                  </svg>
                  打印
                </button>
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"
                    />
                  </svg>
                  收藏
                </button>
                <button className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"
                    />
                  </svg>
                  导出PDF
                </button>
              </div>
            </div>
            <div className="mt-2 flex flex-wrap items-center text-sm text-gray-500 dark:text-gray-400">
              {formattedNoticeTime && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">发布时间：</span>
                  {formattedNoticeTime}
                </div>
              )}
              {bidNotice.tender_project_code && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">项目编号：</span>
                  {bidNotice.tender_project_code}
                </div>
              )}
              <div className="mr-6 mb-2">
                <span className="font-medium">公告类型：</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  招标公告
                </span>
              </div>
              {bidNotice.tenderer_name && (
                <div className="mr-6 mb-2">
                  <span className="font-medium">招标人：</span>
                  {bidNotice.tenderer_name}
                </div>
              )}
            </div>
          </div>

          {/* 详情页内容部分 */}
          <div className="px-4 py-6 sm:px-6">
            {/* 内容区域 - 左右两栏布局 */}
            <div className="py-6 grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* 左侧内容区域 */}
              <div className="md:col-span-3 prose max-w-none dark:prose-invert">
                {/* 公告内容 */}
                {bidNotice.notice_content && (
                  <div className="mb-8">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      公告内容
                    </h3>
                    <div
                      className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap"
                      dangerouslySetInnerHTML={{
                        __html: bidNotice.notice_content,
                      }}
                    />
                  </div>
                )}

                {/* 招标信息 */}
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    招标信息
                  </h3>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                        {bidNotice.tender_project_name && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                              项目名称
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              {bidNotice.tender_project_name}
                            </td>
                          </tr>
                        )}
                        {bidNotice.bid_section_names && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              标段名称
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              {bidNotice.bid_section_names}
                            </td>
                          </tr>
                        )}
                        <tr>
                          <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                            招标人
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                            {bidNotice.tenderer_name || "-"}
                          </td>
                          <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                            联系人
                          </td>
                          <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                            {bidNotice.tenderer_contactor || "-"}
                          </td>
                        </tr>
                        {bidNotice.tender_agency_name && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              招标代理
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidNotice.tender_agency_name}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              代理联系人
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidNotice.tender_agency_contactor || "-"}
                            </td>
                          </tr>
                        )}
                        {bidNotice.notice_content && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              招标内容
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              <div
                                dangerouslySetInnerHTML={{
                                  __html: bidNotice.notice_content || "",
                                }}
                              />
                            </td>
                          </tr>
                        )}
                        {bidNotice && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              最高限价（元）
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidNotice.controlled_price || "\\"}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              工期（天）
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {bidNotice.time_limit || "-"}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 重要时间节点 */}
                <div className="mt-6 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    重要时间节点
                  </h3>
                  <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                      <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                        {formattedDocGetStartTime && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                              招标文件获取开始时间
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {formattedDocGetStartTime}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 w-1/4">
                              招标文件获取截止时间
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {formattedDocGetEndTime || "-"}
                            </td>
                          </tr>
                        )}
                        {formattedReferEndTime && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              投标文件递交截止时间
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {formattedReferEndTime}
                            </td>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              开标时间
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                              {formattedOpenTime || "-"}
                            </td>
                          </tr>
                        )}
                        {bidNotice.bid_open_address && (
                          <tr>
                            <td className="px-4 py-3 bg-gray-50 dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300">
                              开标地点
                            </td>
                            <td
                              className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100"
                              colSpan={3}
                            >
                              {bidNotice.bid_open_address}
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>

                {/* 资格要求 */}
                {bidNotice.bid_qual && (
                  <div className="mt-6 border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      投标人资格要求
                    </h3>
                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {bidNotice.bid_qual}
                      </p>
                    </div>
                  </div>
                )}

                {/* 附件 */}
                <div className="mt-6 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    招标文件
                  </h3>
                  {files.length > 0 ? (
                    <ul className="divide-y divide-gray-200 dark:divide-gray-700">
                      {files.map((file, index) => (
                        <li key={index} className="py-4">
                          <div className="flex items-center space-x-4">
                            <div className="flex-shrink-0">
                              <File className="h-6 w-6 text-gray-400" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                                {file.file_name}
                                {file.file_type}
                              </p>
                            </div>
                            <div>
                              <a
                                href={file.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                              >
                                下载
                              </a>
                            </div>
                          </div>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 dark:text-gray-400">暂无附件</p>
                  )}
                </div>

                {/* 获取方式 */}
                {(bidNotice.tender_doc_get_method ||
                  bidNotice.bid_doc_refer_method) && (
                  <div className="mt-6 border-t border-gray-200 pt-6">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                      获取方式
                    </h3>
                    <div className="space-y-4">
                      {bidNotice.tender_doc_get_method && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            招标文件获取方式
                          </h4>
                          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                            <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                              {bidNotice.tender_doc_get_method}
                            </p>
                          </div>
                        </div>
                      )}
                      {bidNotice.bid_doc_refer_method && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            投标文件递交方式
                          </h4>
                          <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                            <p className="text-sm text-gray-600 dark:text-gray-400 whitespace-pre-wrap">
                              {bidNotice.bid_doc_refer_method}
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 基本信息 */}
                <div className="mt-8 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    基本信息
                  </h3>
                  <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                    {bidNotice.notice_media && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          来源媒体
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {bidNotice.notice_media}
                        </dd>
                      </div>
                    )}
                    {bidNotice.tender_project_address && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          项目地点
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {bidNotice.tender_project_address}
                        </dd>
                      </div>
                    )}
                    {bidNotice.fund_source && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          资金来源
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {bidNotice.fund_source}
                        </dd>
                      </div>
                    )}
                    {bidNotice.is_electronic_bid && (
                      <div className="sm:col-span-1">
                        <dt className="text-sm font-medium text-gray-500 dark:text-gray-400">
                          是否电子投标
                        </dt>
                        <dd className="mt-1 text-sm text-gray-900 dark:text-white">
                          {bidNotice.is_electronic_bid}
                        </dd>
                      </div>
                    )}
                  </dl>
                </div>

                {/* 相关链接 */}
                <div className="mt-6 border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    相关推荐
                  </h3>
                  <ul className="space-y-2">
                    <li>
                      <Link
                        href="/notices/1?noticeType=招标公告"
                        className="text-blue-600 hover:underline dark:text-blue-400"
                      >
                        查看更多招标公告
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="/notices/1"
                        className="text-blue-600 hover:underline dark:text-blue-400"
                      >
                        返回公告列表
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>

              {/* 右侧区域 - 招标热词 */}
              <div className="md:col-span-1">
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 sticky top-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                    招标热词
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 cursor-pointer">
                      建筑工程
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer">
                      市政工程
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-purple-100 text-purple-800 hover:bg-purple-200 cursor-pointer">
                      设计方案
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-yellow-100 text-yellow-800 hover:bg-yellow-200 cursor-pointer">
                      项目咨询
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-red-100 text-red-800 hover:bg-red-200 cursor-pointer">
                      工程项目
                    </span>
                    <span className="inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-indigo-100 text-indigo-800 hover:bg-indigo-200 cursor-pointer">
                      车辆采购
                    </span>
                  </div>

                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mt-6 mb-3">
                    热门公告
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                      >
                        广州市车辆绿化工程及设施采购招标公告
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                      >
                        2025年江苏省交通设施维护工程招标
                      </Link>
                    </li>
                    <li>
                      <Link
                        href="#"
                        className="text-sm text-blue-600 hover:underline dark:text-blue-400"
                      >
                        北京市公园绿化管理服务项目招标
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
