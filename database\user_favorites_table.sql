-- 用户收藏表
CREATE TABLE IF NOT EXISTS user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL COMMENT '用户ID',
    notice_id VARCHAR(100) NOT NULL COMMENT '公告ID',
    notice_title VARCHAR(500) NOT NULL COMMENT '公告标题',
    notice_type ENUM('plan', 'tender', 'win', 'other') DEFAULT 'plan' COMMENT '公告类型',
    content_preview TEXT COMMENT '内容预览',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    
    -- 索引
    INDEX idx_user_id (user_id),
    INDEX idx_notice_id (notice_id),
    INDEX idx_notice_type (notice_type),
    INDEX idx_created_at (created_at),
    
    -- 唯一约束：防止重复收藏
    UNIQUE KEY uk_user_notice (user_id, notice_id),
    
    -- 外键约束（如果有用户表的话）
    -- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    
    COMMENT='用户收藏表'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些示例数据（可选）
-- INSERT INTO user_favorites (user_id, notice_id, notice_title, notice_type, content_preview) VALUES
-- (1, 'NOTICE001', '某某工程招标公告', 'plan', '这是一个重要的工程招标项目...'),
-- (1, 'NOTICE002', '某某设备采购招标', 'tender', '采购一批重要设备...');

-- 查询用户收藏统计
-- SELECT 
--     notice_type,
--     COUNT(*) as count
-- FROM user_favorites 
-- WHERE user_id = 1 
-- GROUP BY notice_type;
