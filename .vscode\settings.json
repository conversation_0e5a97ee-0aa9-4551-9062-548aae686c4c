{
    "mcp": {
        "inputs": [],
        "servers": {
            "mcp-server-time": {
                "command": "python",
                "args": [
                    "-m",
                    "mcp_server_time",
                    "--local-timezone=America/Los_Angeles"
                ],
                "env": {}
            },
             "bid_mysql": {
      "command": "npx",
      "args": ["-y", "@benborla29/mcp-server-mysql"],
      "env": {
        "MYSQL_HOST": "rm-wz9v5eg93b6dx06219o.mysql.rds.aliyuncs.com",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "bs",
        "MYSQL_PASS": "bs-lin!!0",
        "MYSQL_DB": "bs-data"
      }
    },
        }
    },
    "chat.mcp.discovery.enabled": true
}