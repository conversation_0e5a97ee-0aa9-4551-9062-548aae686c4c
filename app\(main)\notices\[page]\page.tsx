import React from "react";
import Link from "next/link";
import { notFound } from "next/navigation";
import { ChevronLeft, Search } from "lucide-react";
import { executeQuery } from "@/lib/db";
import { cn } from "@/lib/utils";
import RegionSelector from "@/components/RegionSelector";
import NoticesSearchPage from "../NoticesSearchPage";

// Filter categories
const categories = [
  { id: "category", name: "信息栏目" },
  { id: "searchType", name: "搜索类型" },
  { id: "time", name: "发布时间" },
  { id: "region", name: "项目地区" },
];

// Filter options
const categoryOptions = [
  { id: "bidding", name: "招标采购" },
  { id: "inquiry", name: "项目询价" },
  { id: "qa", name: "变更答疑" },
  { id: "winner", name: "中标公示" },
];

const searchTypeOptions = [
  { id: "full", name: "全文搜索" },
  { id: "title", name: "标题检索" },
];

const timeOptions = [
  { id: "week", name: "近一周" },
  { id: "month", name: "近一月" },
  { id: "three_months", name: "近三月" },
  { id: "half_year", name: "近半年" },
  { id: "year", name: "近一年" },
];

const regionOptions = [
  { id: "all", name: "全国" },
  {
    id: "north",
    name: "华北",
    subOptions: ["北京", "天津", "河北", "山西", "内蒙古"],
  },
  {
    id: "east",
    name: "华东",
    subOptions: ["上海", "江苏", "浙江", "安徽", "福建", "江西", "山东"],
  },
  { id: "south", name: "华南", subOptions: ["广东", "广西", "海南"] },
  { id: "central", name: "华中", subOptions: ["河南", "湖北", "湖南"] },
  {
    id: "southwest",
    name: "西南",
    subOptions: ["重庆", "四川", "贵州", "云南", "西藏"],
  },
  {
    id: "northwest",
    name: "西北",
    subOptions: ["陕西", "甘肃", "青海", "宁夏", "新疆"],
  },
  { id: "northeast", name: "东北", subOptions: ["辽宁", "吉林", "黑龙江"] },
];

// Hot keywords
const hotKeywords = [
  "交通",
  "广告",
  "绿化",
  "监控",
  "物业",
  "工程",
  "装修",
  "软件",
];

// Helper function to format date from YYYYMMDD to YYYY-MM-DD
function formatDate(dateString: string): string {
  if (!dateString || dateString.length !== 8) return "";
  const year = dateString.substring(0, 4);
  const month = dateString.substring(4, 6);
  const day = dateString.substring(6, 8);
  return `${year}-${month}-${day}`;
}

// Type definitions
interface Notice {
  id: number;
  notice_id: string;
  notice_title: string;
  notice_type_desc: string;
  province: string;
  publish_date: string;
  publishDate: string; // Formatted date
}

interface PageProps {
  params: Promise<{
    page: string;
  }>;
}

export default async function NoticesPage({
  params,
  searchParams,
}: {
  params: Promise<{ page: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  const p = await params;
  const s = await searchParams;

  return <NoticesSearchPage params={p} searchParams={s} baseUrl={"/notices"} />;
}
