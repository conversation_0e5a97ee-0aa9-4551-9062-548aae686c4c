import React from "react";
import { notFound } from "next/navigation";
import SearchOptions from "@/components/SearchOptions";
import NoticesDisplay from "@/app/(main)/notices/NoticesDisplay";

export default async function NoticesSearchPage({
    baseUrl,
    params,
    searchParams,
}: {
    baseUrl: string;
    params: { page: string };
    searchParams: { [key: string]: string | string[] | undefined };
}) {
    const currentPage = parseInt(params.page) || 1;

    // 验证页码
    if (isNaN(currentPage) || currentPage < 1) {
        return notFound();
    }

    // 提取搜索参数
    const keyword = Array.isArray(searchParams.keyword)
        ? searchParams.keyword[0]
        : searchParams.keyword || "";

    const searchTypes = Array.isArray(searchParams.searchType)
        ? searchParams.searchType
        : searchParams.searchType
            ? [searchParams.searchType]
            : [];

    const regionFilter = searchParams.region as string;
    const provinceFilter = searchParams.province;
    const selectedProvinces = Array.isArray(provinceFilter)
        ? provinceFilter
        : provinceFilter
            ? [provinceFilter]
            : [];

    const timeFilter = (searchParams.time as string) || "three_months";

    return (
        <div className="bg-gray-50 dark:bg-gray-900">
            {/* 标题部分 */}
            {/* 使用SearchOptions组件 */}
            <SearchOptions
                initialKeyword={keyword}
                initialSearchTypes={searchTypes}
                initialTimeFilter={timeFilter}
                initialRegion={regionFilter}
                initialProvinces={selectedProvinces}
                baseUrl={baseUrl}
            />

            {/* 使用NoticesDisplay组件 */}
            <NoticesDisplay currentPage={currentPage} searchParams={searchParams} />
        </div>
    );
}
