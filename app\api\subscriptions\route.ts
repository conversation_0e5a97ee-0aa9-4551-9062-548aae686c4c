import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

// Types for subscription data
interface SubscriptionData {
  name: string;
  keywords: string[];
  regions: string[];
  categories: string[];
  subscription_type: 'tender_announcement' | 'winning_announcement' | 'both';
  min_amount?: number;
  max_amount?: number;
  wechat_push: boolean;
  email_push: boolean;
  push_frequency: 'realtime' | 'daily' | 'custom';
  push_times?: string[];
  daily_push_enabled: boolean;
  realtime_push_enabled: boolean;
  realtime_push_start_time?: string;
  realtime_push_end_time?: string;
}

interface Subscription extends SubscriptionData {
  id: number;
  user_id: number;
  is_active: boolean;
  match_count: number;
  last_matched: string | null;
  created_at: string;
  updated_at: string;
}

// Helper function to verify JWT token and get user ID
async function getUserFromToken(request: NextRequest): Promise<number | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    return decoded.user_id || decoded.id;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// GET - Fetch user's subscriptions
export async function GET(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const subscriptions = await executeQuery<Subscription[]>(
      `SELECT 
        id, user_id, name, keywords, regions, categories, subscription_type,
        min_amount, max_amount, wechat_push, email_push, push_frequency,
        push_times, daily_push_enabled, realtime_push_enabled,
        realtime_push_start_time, realtime_push_end_time,
        is_active, match_count, last_matched, created_at, updated_at
      FROM user_subscriptions 
      WHERE user_id = ? 
      ORDER BY created_at DESC`,
      [userId]
    );

    return NextResponse.json({
      success: true,
      data: subscriptions
    });
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscriptions' },
      { status: 500 }
    );
  }
}

// POST - Create new subscription
export async function POST(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: SubscriptionData = await request.json();

    // Validate required fields
    if (!body.name || !body.keywords || body.keywords.length === 0) {
      return NextResponse.json(
        { error: 'Name and keywords are required' },
        { status: 400 }
      );
    }

    // Check subscription limit based on user membership
    const userInfo = await executeQuery<any[]>(
      'SELECT membership_type, subscription_limit FROM users WHERE id = ?',
      [userId]
    );

    if (userInfo.length === 0) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const subscriptionLimit = userInfo[0].subscription_limit || 3;
    const currentCount = await executeQuery<any[]>(
      'SELECT COUNT(*) as count FROM user_subscriptions WHERE user_id = ? AND is_active = 1',
      [userId]
    );

    if (currentCount[0].count >= subscriptionLimit) {
      return NextResponse.json(
        { error: `Subscription limit reached. Maximum ${subscriptionLimit} subscriptions allowed.` },
        { status: 400 }
      );
    }

    // Insert new subscription
    const result = await executeQuery(
      `INSERT INTO user_subscriptions (
        user_id, name, keywords, regions, categories, subscription_type,
        min_amount, max_amount, wechat_push, email_push, push_frequency,
        push_times, daily_push_enabled, realtime_push_enabled,
        realtime_push_start_time, realtime_push_end_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId,
        body.name,
        JSON.stringify(body.keywords),
        JSON.stringify(body.regions || []),
        JSON.stringify(body.categories || []),
        body.subscription_type || 'both',
        body.min_amount || null,
        body.max_amount || null,
        body.wechat_push ? 1 : 0,
        body.email_push ? 1 : 0,
        body.push_frequency || 'daily',
        JSON.stringify(body.push_times || []),
        body.daily_push_enabled ? 1 : 0,
        body.realtime_push_enabled ? 1 : 0,
        body.realtime_push_start_time || '08:00:00',
        body.realtime_push_end_time || '17:00:00'
      ]
    );

    return NextResponse.json({
      success: true,
      message: 'Subscription created successfully',
      data: { id: (result as any).insertId }
    });
  } catch (error) {
    console.error('Error creating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to create subscription' },
      { status: 500 }
    );
  }
}

// PUT - Update subscription
export async function PUT(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, ...updateData } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    // Verify ownership
    const subscription = await executeQuery<any[]>(
      'SELECT user_id FROM user_subscriptions WHERE id = ?',
      [id]
    );

    if (subscription.length === 0) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }

    if (subscription[0].user_id !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Build update query dynamically
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    Object.keys(updateData).forEach(key => {
      if (updateData[key] !== undefined) {
        if (['keywords', 'regions', 'categories', 'push_times'].includes(key)) {
          updateFields.push(`${key} = ?`);
          updateValues.push(JSON.stringify(updateData[key]));
        } else if (['wechat_push', 'email_push', 'daily_push_enabled', 'realtime_push_enabled'].includes(key)) {
          updateFields.push(`${key} = ?`);
          updateValues.push(updateData[key] ? 1 : 0);
        } else {
          updateFields.push(`${key} = ?`);
          updateValues.push(updateData[key]);
        }
      }
    });

    if (updateFields.length === 0) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      );
    }

    updateValues.push(id);

    await executeQuery(
      `UPDATE user_subscriptions SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP WHERE id = ?`,
      updateValues
    );

    return NextResponse.json({
      success: true,
      message: 'Subscription updated successfully'
    });
  } catch (error) {
    console.error('Error updating subscription:', error);
    return NextResponse.json(
      { error: 'Failed to update subscription' },
      { status: 500 }
    );
  }
}

// DELETE - Delete subscription
export async function DELETE(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Subscription ID is required' },
        { status: 400 }
      );
    }

    // Verify ownership
    const subscription = await executeQuery<any[]>(
      'SELECT user_id FROM user_subscriptions WHERE id = ?',
      [id]
    );

    if (subscription.length === 0) {
      return NextResponse.json(
        { error: 'Subscription not found' },
        { status: 404 }
      );
    }

    if (subscription[0].user_id !== userId) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Soft delete by setting is_active to false
    await executeQuery(
      'UPDATE user_subscriptions SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );

    return NextResponse.json({
      success: true,
      message: 'Subscription deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting subscription:', error);
    return NextResponse.json(
      { error: 'Failed to delete subscription' },
      { status: 500 }
    );
  }
}
