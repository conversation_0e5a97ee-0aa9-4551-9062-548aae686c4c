"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import userService from "@/lib/userService";
import SubscriptionRegionSelector from "@/components/SubscriptionRegionSelector";
import {
  Plus,
  X,
  Save,
  ArrowLeft,
  Bell,
  Mail,
  Clock,
  MapPin,
  Tag,
  DollarSign
} from "lucide-react";

interface Region {
  id: number;
  code: string;
  name: string;
  parent_code: string | null;
  level: number;
  children?: Region[];
}

interface SubscriptionForm {
  name: string;
  keywords: string[];
  regions: string[];
  categories: string[];
  subscription_type: 'tender_announcement' | 'winning_announcement' | 'both';
  min_amount: string;
  max_amount: string;
  wechat_push: boolean;
  email_push: boolean;
  push_frequency: 'realtime' | 'daily' | 'custom';
  push_times: string[];
  daily_push_enabled: boolean;
  realtime_push_enabled: boolean;
  realtime_push_start_time: string;
  realtime_push_end_time: string;
}

export default function NewSubscriptionPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [regions, setRegions] = useState<Region[]>([]);
  const [newKeyword, setNewKeyword] = useState("");
  const [newPushTime, setNewPushTime] = useState("");
  
  const [formData, setFormData] = useState<SubscriptionForm>({
    name: "",
    keywords: [],
    regions: [],
    categories: [],
    subscription_type: 'both',
    min_amount: "",
    max_amount: "",
    wechat_push: true,
    email_push: true,
    push_frequency: 'daily',
    push_times: ['10:00', '14:00', '17:00'],
    daily_push_enabled: true,
    realtime_push_enabled: false,
    realtime_push_start_time: '08:00',
    realtime_push_end_time: '17:00'
  });

  useEffect(() => {
    if (!userService.isLoggedIn()) {
      router.push("/login");
      return;
    }
  }, []);

  const handleInputChange = (field: keyof SubscriptionForm, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addKeyword = () => {
    if (newKeyword.trim() && formData.keywords.length < 5) {
      setFormData(prev => ({
        ...prev,
        keywords: [...prev.keywords, newKeyword.trim()]
      }));
      setNewKeyword("");
    }
  };

  const removeKeyword = (index: number) => {
    setFormData(prev => ({
      ...prev,
      keywords: prev.keywords.filter((_, i) => i !== index)
    }));
  };

  const addPushTime = () => {
    if (newPushTime && !formData.push_times.includes(newPushTime)) {
      setFormData(prev => ({
        ...prev,
        push_times: [...prev.push_times, newPushTime].sort()
      }));
      setNewPushTime("");
    }
  };

  const removePushTime = (time: string) => {
    setFormData(prev => ({
      ...prev,
      push_times: prev.push_times.filter(t => t !== time)
    }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('请输入订阅名称');
      return;
    }
    
    if (formData.keywords.length === 0) {
      alert('请至少添加一个关键词');
      return;
    }

    setIsLoading(true);
    
    try {
      const submitData = {
        ...formData,
        min_amount: formData.min_amount ? parseFloat(formData.min_amount) : undefined,
        max_amount: formData.max_amount ? parseFloat(formData.max_amount) : undefined
      };

      const response = await fetch('/api/subscriptions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      const data = await response.json();
      if (data.success) {
        router.push('/profile/subscriptions');
      } else {
        alert('创建失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error creating subscription:', error);
      alert('创建失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-700 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              返回
            </button>
          </div>
          <h1 className="mt-4 text-2xl font-bold text-gray-900 dark:text-white">新建订阅</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            创建新的订阅来接收相关招投标信息推送
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">基本信息</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  订阅名称 <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  required
                  className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="请输入订阅名称"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  项目类型 <span className="text-red-500">*</span>
                </label>
                <select
                  className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  value={formData.subscription_type}
                  onChange={(e) => handleInputChange('subscription_type', e.target.value)}
                >
                  <option value="both">全部</option>
                  <option value="tender_announcement">招标公告</option>
                  <option value="winning_announcement">中标公告</option>
                </select>
              </div>
            </div>
          </div>

          {/* Keywords */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <Tag className="w-5 h-5 mr-2" />
              订阅关键词 <span className="text-red-500 ml-1">*</span>
            </h2>
            
            <div className="space-y-4">
              <div className="flex space-x-2">
                <input
                  type="text"
                  className="flex-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="输入关键词，最多5个"
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                />
                <button
                  type="button"
                  onClick={addKeyword}
                  disabled={formData.keywords.length >= 5}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
                >
                  <Plus className="w-4 h-4" />
                </button>
              </div>
              
              <div className="text-sm text-gray-500 dark:text-gray-400">
                已添加 {formData.keywords.length}/5 个关键词
              </div>
              
              {formData.keywords.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.keywords.map((keyword, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                    >
                      {keyword}
                      <button
                        type="button"
                        onClick={() => removeKeyword(index)}
                        className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-600 dark:text-blue-400 hover:bg-blue-200"
                      >
                        <X className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Regions */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <MapPin className="w-5 h-5 mr-2" />
              信息地区
            </h2>

            <div className="space-y-4">
              <SubscriptionRegionSelector
                selectedRegions={formData.regions}
                onRegionChange={(regions) => handleInputChange('regions', regions)}
                placeholder="选择订阅地区"
                showSelectAll={true}
              />

              <div className="text-sm text-gray-500 dark:text-gray-400">
                已选择 {formData.regions.length} 个地区
                {formData.regions.length === 0 && "，默认为全国"}
              </div>

              {formData.regions.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {formData.regions.map((region, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {region}
                      <button
                        type="button"
                        onClick={() => {
                          const newRegions = formData.regions.filter(r => r !== region);
                          handleInputChange('regions', newRegions);
                        }}
                        className="ml-1 inline-flex items-center justify-center w-3 h-3 rounded-full text-blue-600 dark:text-blue-400 hover:bg-blue-200"
                      >
                        <X className="w-2 h-2" />
                      </button>
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Amount Range */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <DollarSign className="w-5 h-5 mr-2" />
              金额范围（可选）
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">最低金额（万元）</label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0"
                  value={formData.min_amount}
                  onChange={(e) => handleInputChange('min_amount', e.target.value)}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">最高金额（万元）</label>
                <input
                  type="number"
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="不限"
                  value={formData.max_amount}
                  onChange={(e) => handleInputChange('max_amount', e.target.value)}
                />
              </div>
            </div>
          </div>

          {/* Push Settings */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
              <Bell className="w-5 h-5 mr-2" />
              推送管理
            </h2>
            
            <div className="space-y-6">
              {/* Push Methods */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">推送方式</label>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="wechat-push"
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                      checked={formData.wechat_push}
                      onChange={(e) => handleInputChange('wechat_push', e.target.checked)}
                    />
                    <label htmlFor="wechat-push" className="ml-2 text-sm text-gray-700 dark:text-gray-300 flex items-center">
                      <Bell className="w-4 h-4 mr-1 text-green-600 dark:text-green-400" />
                      微信推送
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="email-push"
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"
                      checked={formData.email_push}
                      onChange={(e) => handleInputChange('email_push', e.target.checked)}
                    />
                    <label htmlFor="email-push" className="ml-2 text-sm text-gray-700 dark:text-gray-300 flex items-center">
                      <Mail className="w-4 h-4 mr-1 text-blue-600 dark:text-blue-400" />
                      邮件推送
                    </label>
                  </div>
                </div>
              </div>

              {/* Push Frequency */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">推送频率</label>
                <div className="space-y-2">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="daily-push"
                      name="push_frequency"
                      value="daily"
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                      checked={formData.push_frequency === 'daily'}
                      onChange={(e) => handleInputChange('push_frequency', e.target.value)}
                    />
                    <label htmlFor="daily-push" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      每日推送
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="realtime-push"
                      name="push_frequency"
                      value="realtime"
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                      checked={formData.push_frequency === 'realtime'}
                      onChange={(e) => handleInputChange('push_frequency', e.target.value)}
                    />
                    <label htmlFor="realtime-push" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      实时推送
                    </label>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="custom-push"
                      name="push_frequency"
                      value="custom"
                      className="h-4 w-4 text-blue-600 dark:text-blue-400 focus:ring-blue-500 border-gray-300 dark:border-gray-600"
                      checked={formData.push_frequency === 'custom'}
                      onChange={(e) => handleInputChange('push_frequency', e.target.value)}
                    />
                    <label htmlFor="custom-push" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                      自定义推送
                    </label>
                  </div>
                </div>
              </div>

              {/* Custom Push Times */}
              {formData.push_frequency === 'custom' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">推送时间</label>
                  <div className="space-y-4">
                    <div className="flex space-x-2">
                      <input
                        type="time"
                        className="border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        value={newPushTime}
                        onChange={(e) => setNewPushTime(e.target.value)}
                      />
                      <button
                        type="button"
                        onClick={addPushTime}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                    
                    {formData.push_times.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {formData.push_times.map((time) => (
                          <span
                            key={time}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                          >
                            <Clock className="w-3 h-3 mr-1" />
                            {time}
                            <button
                              type="button"
                              onClick={() => removePushTime(time)}
                              className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-green-600 dark:text-green-400 hover:bg-green-200"
                            >
                              <X className="w-3 h-3" />
                            </button>
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <button
              type="button"
              onClick={() => router.back()}
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-700"
            >
              取消
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400"
            >
              {isLoading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isLoading ? '创建中...' : '立即订阅'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
