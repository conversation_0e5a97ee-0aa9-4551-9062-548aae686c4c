import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import jwt from 'jsonwebtoken';

// Helper function to verify JWT token and get user ID
async function getUserFromToken(request: NextRequest): Promise<number | null> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;
    return decoded.user_id || decoded.id;
  } catch (error) {
    console.error('Token verification failed:', error);
    return null;
  }
}

// POST - 添加收藏
export async function POST(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { notice_id, notice_title, notice_type, content_preview } = body;

    if (!notice_id || !notice_title) {
      return NextResponse.json(
        { error: 'Notice ID and title are required' },
        { status: 400 }
      );
    }

    // 检查是否已经收藏
    const existingFavorite = await executeQuery<any[]>(
      'SELECT id FROM user_favorites WHERE user_id = ? AND notice_id = ?',
      [userId, notice_id]
    );

    if (existingFavorite.length > 0) {
      return NextResponse.json(
        { success: false, message: '已经收藏过了' },
        { status: 400 }
      );
    }

    // 添加收藏
    await executeQuery(
      `INSERT INTO user_favorites (
        user_id, notice_id, notice_title, notice_type, content_preview, created_at
      ) VALUES (?, ?, ?, ?, ?, NOW())`,
      [userId, notice_id, notice_title, notice_type || 'plan', content_preview || '']
    );

    return NextResponse.json({
      success: true,
      message: '收藏成功'
    });

  } catch (error) {
    console.error('Error adding favorite:', error);
    return NextResponse.json(
      { error: 'Failed to add favorite' },
      { status: 500 }
    );
  }
}

// GET - 获取用户收藏列表
export async function GET(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // 获取总数
    const countResult = await executeQuery<{ count: number }[]>(
      'SELECT COUNT(*) as count FROM user_favorites WHERE user_id = ?',
      [userId]
    );
    const total = countResult[0].count;

    // 获取收藏列表
    const favorites = await executeQuery<any[]>(
      `SELECT 
        id, notice_id, notice_title, notice_type, content_preview, created_at
       FROM user_favorites 
       WHERE user_id = ? 
       ORDER BY created_at DESC 
       LIMIT ? OFFSET ?`,
      [userId, limit, offset]
    );

    return NextResponse.json({
      success: true,
      data: favorites,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Error fetching favorites:', error);
    return NextResponse.json(
      { error: 'Failed to fetch favorites' },
      { status: 500 }
    );
  }
}

// DELETE - 删除收藏
export async function DELETE(request: NextRequest) {
  try {
    const userId = await getUserFromToken(request);
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const favoriteId = searchParams.get('id');
    const noticeId = searchParams.get('notice_id');

    if (!favoriteId && !noticeId) {
      return NextResponse.json(
        { error: 'Favorite ID or Notice ID is required' },
        { status: 400 }
      );
    }

    let query = 'DELETE FROM user_favorites WHERE user_id = ?';
    let params = [userId];

    if (favoriteId) {
      query += ' AND id = ?';
      params.push(favoriteId);
    } else if (noticeId) {
      query += ' AND notice_id = ?';
      params.push(noticeId);
    }

    const result = await executeQuery(query, params);

    if (result.affectedRows === 0) {
      return NextResponse.json(
        { success: false, message: '收藏不存在或已删除' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: '取消收藏成功'
    });

  } catch (error) {
    console.error('Error removing favorite:', error);
    return NextResponse.json(
      { error: 'Failed to remove favorite' },
      { status: 500 }
    );
  }
}
