# 招投标信息平台

这是一个基于 Next.js 15 构建的现代化招投标信息平台，提供招标公告查询、订阅管理、用户反馈等功能。

## 🚀 新增功能

### 1. Dark Mode 支持
- ✅ 完整的深色模式支持
- ✅ 系统主题自动切换
- ✅ 导航栏主题切换按钮
- ✅ 所有页面和组件的深色模式适配

### 2. UI/UX 提升
- ✅ 使用 shadcn/ui 组件库，提供一致的设计语言
- ✅ 完整的 Toast 通知系统
- ✅ 加载状态和错误处理
- ✅ 响应式设计，完美支持移动端
- ✅ 优雅的动画和过渡效果

### 3. 意见反馈功能
- ✅ 用户反馈提交页面
- ✅ 多种反馈类型（功能建议、问题反馈、投诉建议、表扬建议、其他）
- ✅ 5星评分系统
- ✅ 匿名反馈选项
- ✅ 联系方式可选填写
- ✅ 个人中心反馈管理

## 🛠️ 技术栈

- **前端**: Next.js 15 + React 19
- **样式**: Tailwind CSS + shadcn/ui
- **主题**: next-themes
- **图标**: Lucide React
- **数据库**: MySQL
- **认证**: JWT
- **类型安全**: TypeScript

## 📦 安装和运行

1. 克隆项目
```bash
git clone <repository-url>
cd bid-web
```

2. 安装依赖
```bash
npm install
```

3. 配置环境变量
```bash
cp .env.example .env.local
# 编辑 .env.local 文件，填入正确的配置信息
```

4. 初始化数据库
```bash
# 执行数据库更新脚本
mysql -h your_host -u your_user -p your_database < database/schema_updates.sql
```

5. 启动开发服务器
```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 🎯 主要功能

### 前台功能
- 招标公告查询和浏览
- 中标结果查询
- 用户注册和登录
- 个人中心管理
- 订阅管理
- 推送设置
- 我的收藏
- 浏览记录
- 会员权益
- 意见反馈



## 📱 页面路由

### 前台页面
- `/` - 首页
- `/login` - 登录页面
- `/register` - 注册页面
- `/feedback` - 意见反馈页面
- `/profile` - 个人中心
- `/profile/subscriptions` - 订阅管理
- `/profile/settings` - 推送设置
- `/profile/favorites` - 我的收藏
- `/profile/history` - 浏览记录
- `/profile/membership` - 会员权益
- `/profile/feedback` - 我的反馈



## 🗄️ 数据库结构

新增的数据库表：
- `user_feedback` - 用户反馈表
- `user_favorites` - 用户收藏表
- `user_browsing_history` - 用户浏览记录表

## 🎨 UI 组件

使用 shadcn/ui 组件库，包含：
- Button, Card, Input, Textarea
- Select, Checkbox, Radio Group
- Dialog, Badge, Label
- Toast 通知系统
- Theme Toggle 主题切换

## 🌙 主题系统

- 支持亮色/暗色/系统主题
- 自动跟随系统主题变化
- 所有组件完全适配深色模式
- 平滑的主题切换动画

## 📝 环境变量

参考 `.env.example` 文件配置以下环境变量：
- 数据库连接信息
- JWT 密钥
- API 基础URL
- 邮件/短信/微信推送配置（可选）

## 🚀 部署

1. 构建项目
```bash
npm run build
```

2. 启动生产服务器
```bash
npm start
```

## 🎉 新功能完成情况

### ✅ 已完成功能

#### 1. Dark Mode 深色模式
- ✅ 完整的深色模式支持，使用 `next-themes`
- ✅ 导航栏添加主题切换按钮
- ✅ 所有页面和组件完全适配深色模式
- ✅ 自动跟随系统主题设置
- ✅ 平滑的主题切换动画

#### 2. 后台管理系统
- ✅ 管理功能集成到个人中心 (`/profile/admin`)
- ✅ 管理员仪表板，显示系统统计信息
- ✅ 用户管理页面 (`/profile/admin/users`)
- ✅ 意见反馈管理 (`/profile/admin/feedback`)
- ✅ 系统设置页面 (`/profile/admin/settings`)
- ✅ 权限控制：只有管理员用户才能看到管理菜单
- ✅ 统一的侧边栏导航，无需单独后台入口
- ✅ 响应式设计，支持移动端访问

#### 3. 意见反馈功能
- ✅ 用户反馈提交页面 (`/profile/feedback/new`)
- ✅ 支持多种反馈类型：功能建议、问题反馈、投诉建议、表扬建议、其他
- ✅ 5星评分系统
- ✅ 匿名反馈选项
- ✅ 联系方式可选填写
- ✅ 个人中心反馈管理 (`/profile/feedback`)
- ✅ 管理员回复功能
- ✅ 反馈状态管理（待处理、处理中、已回复、已解决、已关闭）

#### 4. UI/UX 提升
- ✅ 使用 shadcn/ui 组件库，提供一致的设计语言
- ✅ 完整的 Toast 通知系统
- ✅ 加载状态和错误处理
- ✅ 响应式设计，完美支持移动端
- ✅ 优雅的动画和过渡效果

### 📊 数据库更新

新增数据库表：
```sql
-- 用户反馈表
CREATE TABLE user_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    type ENUM('suggestion', 'bug', 'complaint', 'praise', 'other') NOT NULL DEFAULT 'suggestion',
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    contact_info VARCHAR(255) NULL,
    rating TINYINT DEFAULT 5 CHECK (rating >= 1 AND rating <= 5),
    is_anonymous BOOLEAN DEFAULT FALSE,
    status ENUM('pending', 'processing', 'replied', 'resolved', 'closed') DEFAULT 'pending',
    admin_reply TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### 🔧 技术实现

#### 主题系统
- 使用 `next-themes` 实现主题切换
- CSS 变量配合 Tailwind CSS 的 dark: 前缀
- 主题状态持久化到 localStorage

#### 后台管理
- 基于 Next.js App Router 的路由组织
- 管理功能集成到个人中心，统一入口
- 基于用户角色的权限控制
- 响应式侧边栏导航，动态显示管理菜单

#### 意见反馈
- RESTful API 设计
- 支持匿名和登录用户提交
- 完整的状态管理流程
- 管理员回复功能

### 🚀 部署说明

1. 确保数据库已更新：
```bash
mysql -h your_host -u your_user -p your_database < database/schema_updates.sql
```

2. 配置环境变量：
```bash
cp .env.example .env.local
# 编辑 .env.local 填入正确的配置
```

3. 构建和启动：
```bash
npm run build
npm start
```

### 📱 页面路由

#### 新增页面
- `/profile/admin` - 管理员仪表板
- `/profile/admin/users` - 用户管理
- `/profile/admin/feedback` - 意见反馈管理
- `/profile/admin/settings` - 系统设置
- `/profile/feedback` - 我的反馈
- `/profile/feedback/new` - 提交新反馈
- `/profile/downloads` - 我的下载

#### API 接口
- `POST /api/feedback` - 提交反馈
- `GET /api/feedback` - 获取反馈列表（管理员）
- `PATCH /api/feedback` - 更新反馈状态/回复
- `GET /api/user/feedback` - 获取用户反馈
- `GET /api/admin/dashboard-stats` - 仪表板统计

## 📄 许可证

MIT License
