import { ChevronDown, ChevronDownSquare } from "lucide-react";
import Link from "next/link";

interface SubCategory {
  name: string;
  href: string;
}

interface IndustryCategory {
  name: string;
  icon: string;
  href: string;
  subCategories: SubCategory[];
}

// 行业分类数据
const industries: IndustryCategory[] = [
  {
    name: "建筑工程",
    icon: "建筑",
    href: "/industry/construction",
    subCategories: [
      { name: "安装厂", href: "/industry/construction/installation" },
      { name: "办公楼", href: "/industry/construction/office" },
      { name: "变电所", href: "/industry/construction/substation" },
      { name: "别墅", href: "/industry/construction/villa" },
      { name: "博物馆", href: "/industry/construction/museum" },
      { name: "仓库", href: "/industry/construction/warehouse" },
      { name: "厂房", href: "/industry/construction/factory" },
      { name: "除尘", href: "/industry/construction/dust" },
    ],
  },
  {
    name: "能源化工",
    icon: "能源",
    href: "/industry/energy",
    subCategories: [
      { name: "炉灰站", href: "/industry/energy/ash" },
      { name: "锅炉", href: "/industry/energy/boiler" },
      { name: "电厂", href: "/industry/energy/power" },
      { name: "压缩机", href: "/industry/energy/compressor" },
      { name: "电力公司", href: "/industry/energy/company" },
      { name: "电网", href: "/industry/energy/grid" },
      { name: "电网建设", href: "/industry/energy/grid-construction" },
      { name: "反应釜", href: "/industry/energy/reactor" },
    ],
  },
  {
    name: "水利水电",
    icon: "水利",
    href: "/industry/water",
    subCategories: [
      { name: "电力工程", href: "/industry/water/power" },
      { name: "给排水", href: "/industry/water/supply" },
      { name: "给水", href: "/industry/water/water-supply" },
      { name: "供水管网", href: "/industry/water/pipe" },
      { name: "台湾", href: "/industry/water/taiwan" },
      { name: "漳州土", href: "/industry/water/zhangzhou" },
      { name: "坝", href: "/industry/water/dam" },
      { name: "河道", href: "/industry/water/river" },
      { name: "河道治理", href: "/industry/water/river-management" },
    ],
  },
  {
    name: "机械设备",
    icon: "机械",
    href: "/industry/machinery",
    subCategories: [
      { name: "安全阀", href: "/industry/machinery/valve" },
      { name: "安全设备", href: "/industry/machinery/safety" },
      { name: "抽屉", href: "/industry/machinery/drawer" },
      { name: "包装设备", href: "/industry/machinery/packaging" },
      { name: "泵", href: "/industry/machinery/pump" },
      { name: "客车", href: "/industry/machinery/bus" },
      { name: "柴油发电机组", href: "/industry/machinery/generator" },
    ],
  },
  {
    name: "园林绿化",
    icon: "园林",
    href: "/industry/landscaping",
    subCategories: [
      { name: "边坡", href: "/industry/landscaping/slope" },
      { name: "苗圃", href: "/industry/landscaping/nursery" },
      { name: "桥梁", href: "/industry/landscaping/bridge" },
      { name: "村土", href: "/industry/landscaping/village" },
      { name: "除土堆", href: "/industry/landscaping/soil" },
      { name: "除草", href: "/industry/landscaping/weeding" },
      { name: "除渣", href: "/industry/landscaping/slag" },
      { name: "道路", href: "/industry/landscaping/road" },
    ],
  },
  {
    name: "政府采购",
    icon: "政府",
    href: "/industry/government",
    subCategories: [
      { name: "购呀网", href: "/industry/government/platform" },
      { name: "党委", href: "/industry/government/party" },
      { name: "地方志", href: "/industry/government/local" },
      { name: "地税局", href: "/industry/government/tax" },
      { name: "地震局", href: "/industry/government/earthquake" },
      { name: "发改委", href: "/industry/government/reform" },
      { name: "法院", href: "/industry/government/court" },
      { name: "公安局", href: "/industry/government/police" },
    ],
  },
  {
    name: "农林牧渔",
    icon: "农业",
    href: "/industry/agriculture",
    subCategories: [
      { name: "口腔防治", href: "/industry/agriculture/dental" },
      { name: "草坪", href: "/industry/agriculture/lawn" },
      { name: "大棚", href: "/industry/agriculture/greenhouse" },
      { name: "灌溉", href: "/industry/agriculture/irrigation" },
      { name: "防火", href: "/industry/agriculture/fire" },
      { name: "防水工程", href: "/industry/agriculture/waterproof" },
      { name: "肥料", href: "/industry/agriculture/fertilizer" },
      { name: "给水系统", href: "/industry/agriculture/water-system" },
    ],
  },
  {
    name: "通讯电子",
    icon: "通讯",
    href: "/industry/communication",
    subCategories: [
      { name: "办公软件", href: "/industry/communication/office" },
      { name: "便携式计算机", href: "/industry/communication/laptop" },
      { name: "笔记本", href: "/industry/communication/notebook" },
      { name: "笔记本电脑", href: "/industry/communication/laptop-pc" },
      { name: "存储器", href: "/industry/communication/storage" },
      { name: "耦合", href: "/industry/communication/coupling" },
      { name: "存储", href: "/industry/communication/memory" },
      { name: "电阻板", href: "/industry/communication/board" },
    ],
  },
  {
    name: "冶金矿产",
    icon: "冶金",
    href: "/industry/metallurgy",
    subCategories: [
      { name: "材板", href: "/industry/metallurgy/plate" },
      { name: "炊火器", href: "/industry/metallurgy/cooker" },
      { name: "园区", href: "/industry/metallurgy/park" },
      { name: "锻压", href: "/industry/metallurgy/forging" },
      { name: "堆取料机", href: "/industry/metallurgy/machine" },
      { name: "钢材", href: "/industry/metallurgy/steel" },
      { name: "给排机", href: "/industry/metallurgy/pump" },
    ],
  },
  {
    name: "环境保护",
    icon: "环境",
    href: "/industry/environmental",
    subCategories: [
      { name: "环保设备", href: "/industry/environmental/equipment" },
      { name: "环境保护", href: "/industry/environmental/protection" },
      { name: "环境工程", href: "/industry/environmental/engineering" },
      { name: "环境监测", href: "/industry/environmental/monitoring" },
      { name: "环境监测仪器", href: "/industry/environmental/instruments" },
      { name: "环卫", href: "/industry/environmental/sanitation" },
      {
        name: "环卫设备",
        href: "/industry/environmental/sanitation-equipment",
      },
      { name: "环卫设施", href: "/industry/environmental/facilities" },
    ],
  },
  {
    name: "交通运输",
    icon: "交通",
    href: "/industry/transportation",
    subCategories: [
      { name: "搬运车", href: "/industry/transportation/carrier" },
      { name: "保洁车", href: "/industry/transportation/cleaning" },
      { name: "包装袋", href: "/industry/transportation/packaging" },
      { name: "标线", href: "/industry/transportation/marking" },
      { name: "客车车", href: "/industry/transportation/bus" },
      { name: "合叉设备", href: "/industry/transportation/fork" },
      { name: "合格物流", href: "/industry/transportation/logistics" },
      { name: "叉车", href: "/industry/transportation/forklift" },
    ],
  },
  {
    name: "仪器仪表",
    icon: "仪器",
    href: "/industry/instruments",
    subCategories: [
      { name: "测厚仪", href: "/industry/instruments/thickness" },
      { name: "测量仪", href: "/industry/instruments/measuring" },
      { name: "测量计算", href: "/industry/instruments/calculation" },
      { name: "测试仪", href: "/industry/instruments/testing" },
      { name: "DCS", href: "/industry/instruments/dcs" },
      { name: "电池表", href: "/industry/instruments/battery" },
      { name: "电子秤", href: "/industry/instruments/scale" },
      { name: "电子计量", href: "/industry/instruments/metering" },
    ],
  },
];

export default function IndustryNavigation() {
  return (
    <div className="bg-white dark:bg-gray-900 py-12 hidden md:block border-t border-gray-100 dark:border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-xl font-semibold text-center mb-8 text-gray-900 dark:text-gray-100">招标行业导航</h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {industries.map((industry) => (
            <div
              key={industry.name}
              className="border border-gray-100 dark:border-gray-700 bg-white dark:bg-gray-800 rounded-lg p-5 hover:shadow-sm transition-shadow"
            >
              <div className="flex items-center mb-3">
                <span className="text-blue-500 text-lg">
                  <ChevronDownSquare />
                </span>
                <Link
                  href={industry.href}
                  className="text-blue-600 dark:text-blue-400 ml-2 font-medium hover:underline"
                >
                  {industry.name}
                </Link>
              </div>

              <div className="flex flex-wrap gap-x-4 gap-y-2 text-sm">
                {industry.subCategories.map((subCategory) => (
                  <Link
                    key={subCategory.name}
                    href={subCategory.href}
                    className="text-gray-600 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                  >
                    {subCategory.name}
                  </Link>
                ))}
              </div>
            </div>
          ))}
        </div>

        <div className="mt-10 text-center">
          <Link
            href="/industries"
            className="inline-flex items-center justify-center px-5 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600"
          >
            查看更多行业分类
          </Link>
        </div>
      </div>
    </div>
  );
}
