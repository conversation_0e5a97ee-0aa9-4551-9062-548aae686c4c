"use client";

import Link from "next/link";
import Image from "next/image";
import { useEffect, useState } from "react";
import {
  Menu,
  X,
  User,
  MessageCircle,
  Phone,
  LogOut,
  Settings,
} from "lucide-react";
import userService, { type UserData } from "@/lib/userService";
import { ThemeToggle } from "@/components/theme-toggle";

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [user, setUser] = useState<UserData | null>(null);
  const [showUserMenu, setShowUserMenu] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // 检查用户登录状态
  useEffect(() => {
    const checkUser = () => {
      if (userService.isLoggedIn()) {
        const currentUser = userService.getCurrentUser();
        setUser(currentUser);
      } else {
        setUser(null);
      }
    };

    // 初始检查
    checkUser();

    // 监听storage变化（当用户在其他标签页登录/登出时）
    const handleStorageChange = () => {
      checkUser();
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, []);

  const handleLogout = () => {
    userService.logout();
    setUser(null);
    setShowUserMenu(false);
    // 可以添加跳转到首页的逻辑
    window.location.href = "/";
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showUserMenu) {
        const target = event.target as Element;
        if (!target.closest(".user-menu-container")) {
          setShowUserMenu(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [showUserMenu]);

  return (
    <nav
      className={`sticky top-0 z-50 bg-blue-600 ${scrolled ? "shadow-md" : ""}`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <Image
                src="/logo.svg"
                alt="Logo"
                width={48}
                height={48}
                className="mr-3"
              />
              <span className="text-xl font-bold text-white tracking-wide">
                招标通
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/"
              className="text-white hover:text-blue-100 px-3 py-2 text-sm font-medium"
            >
              首页
            </Link>
            <Link
              href="/notices"
              className="text-white hover:text-blue-100 px-3 py-2 text-sm font-medium"
            >
              招标公告
            </Link>
            <Link
              href="/results"
              className="text-white hover:text-blue-100 px-3 py-2 text-sm font-medium"
            >
              中标结果
            </Link>
            <Link
              href="/projects"
              className="text-white hover:text-blue-100 px-3 py-2 text-sm font-medium"
            >
              标准规范
            </Link>
            <Link
              href="/help"
              className="text-white hover:text-blue-100 px-3 py-2 text-sm font-medium"
            >
              帮助中心
            </Link>
          </div>

          {/* Login/User Section */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle />

            {user ? (
              // 已登录状态
              <div className="relative user-menu-container">
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="text-white hover:text-blue-100 flex items-center space-x-2"
                >
                  <User className="h-4 w-4" />
                  <span>{user.contact_name}</span>
                  <span className="text-sm text-blue-200">
                    ({user.company_name})
                  </span>
                </button>

                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg py-1 z-50">
                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <User className="h-4 w-4 mr-2" />
                      个人中心
                    </Link>
                    <Link
                      href="/profile/subscriptions"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      我的订阅
                    </Link>
                    <Link
                      href="/profile/push-records"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Phone className="h-4 w-4 mr-2" />
                      推送记录
                    </Link>
                    <Link
                      href="/profile/settings"
                      className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      <Settings className="h-4 w-4 mr-2" />
                      推送设置
                    </Link>
                    <div className="border-t border-gray-100 my-1"></div>
                    <button
                      onClick={handleLogout}
                      className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <LogOut className="h-4 w-4 mr-2" />
                      退出登录
                    </button>
                  </div>
                )}
              </div>
            ) : (
              // 未登录状态
              <>
                <Link
                  href="/login"
                  className="text-white hover:text-blue-100 flex items-center"
                >
                  <User className="h-4 w-4 mr-1" />
                  <span>登录</span>
                </Link>
                <span className="text-blue-300">|</span>
                <Link
                  href="/register"
                  className="text-white hover:text-blue-100"
                >
                  注册
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="inline-flex items-center justify-center p-2 rounded-md text-white hover:text-blue-100 focus:outline-none"
            >
              {isOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isOpen && (
        <div className="md:hidden bg-blue-700">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            <Link
              href="/"
              className="text-white block px-3 py-2 rounded-md text-base font-medium"
              onClick={() => setIsOpen(false)}
            >
              首页
            </Link>
            <Link
              href="/notices"
              className="text-white block px-3 py-2 rounded-md text-base font-medium"
              onClick={() => setIsOpen(false)}
            >
              招标公告
            </Link>
            <Link
              href="/results"
              className="text-white block px-3 py-2 rounded-md text-base font-medium"
              onClick={() => setIsOpen(false)}
            >
              中标结果
            </Link>
            <Link
              href="/projects"
              className="text-white block px-3 py-2 rounded-md text-base font-medium"
              onClick={() => setIsOpen(false)}
            >
              标准规范
            </Link>
            <Link
              href="/help"
              className="text-white block px-3 py-2 rounded-md text-base font-medium"
              onClick={() => setIsOpen(false)}
            >
              帮助中心
            </Link>
            <div className="border-t border-blue-600 my-2"></div>

            {user ? (
              // 已登录状态
              <>
                <div className="text-white px-3 py-2">
                  <div className="flex items-center space-x-2">
                    <User className="h-4 w-4" />
                    <span>{user.contact_name}</span>
                  </div>
                  <div className="text-sm text-blue-200 ml-6">
                    {user.company_name}
                  </div>
                </div>
                <Link
                  href="/profile"
                  className="text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  个人设置
                </Link>
                <button
                  onClick={() => {
                    handleLogout();
                    setIsOpen(false);
                  }}
                  className="text-white block px-3 py-2 rounded-md text-base font-medium w-full text-left"
                >
                  退出登录
                </button>
              </>
            ) : (
              // 未登录状态
              <>
                <Link
                  href="/login"
                  className="text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  登录
                </Link>
                <Link
                  href="/register"
                  className="text-white block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsOpen(false)}
                >
                  注册
                </Link>
              </>
            )}
          </div>
        </div>
      )}
    </nav>
  );
}
