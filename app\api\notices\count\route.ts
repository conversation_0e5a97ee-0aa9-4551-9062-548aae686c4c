import { NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function GET() {
  try {
    // Get total count for pagination
    const totalResult = await executeQuery<any[]>('SELECT COUNT(*) as total FROM trading_notices');
    const total = totalResult[0].total;

    // Return the total count
    return NextResponse.json({ total });
  } catch (error) {
    console.error('Error fetching notices count:', error);
    return NextResponse.json(
      { error: 'Failed to fetch notices count' },
      { status: 500 }
    );
  }
}
