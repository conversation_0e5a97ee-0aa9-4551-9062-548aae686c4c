import { NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';

export async function GET() {
  try {
    // Query to get trading notices sorted by publish_date
    const rows = await executeQuery(
      `SELECT 
        id, 
        notice_id, 
        notice_title, 
        notice_type_desc, 
        publish_date, 
        site_name, 
        created_at 
      FROM 
        trading_notices 
      ORDER BY 
        publish_date DESC, 
        created_at DESC 
      LIMIT 50`
    );
    
    // Return the data as JSON
    return NextResponse.json(rows);
  } catch (error) {
    console.error('Database error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch trading notices' },
      { status: 500 }
    );
  }
}
