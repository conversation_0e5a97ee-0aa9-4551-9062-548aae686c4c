"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import userService from "@/lib/userService";
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Clock, 
  Plus, 
  X, 
  Save,
  ArrowLeft,
  Volume2,
  VolumeX,
  Moon,
  Sun
} from "lucide-react";

interface UserSettings {
  email_notifications: boolean;
  sms_notifications: boolean;
  wechat_notifications: boolean;
  push_notifications: boolean;
  daily_push_times: string[];
  custom_push_settings: any;
  notification_sound: boolean;
  do_not_disturb_start: string;
  do_not_disturb_end: string;
  favorite_regions: string[];
  favorite_categories: string[];
}

export default function SettingsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [newPushTime, setNewPushTime] = useState("");
  
  const [settings, setSettings] = useState<UserSettings>({
    email_notifications: true,
    sms_notifications: true,
    wechat_notifications: true,
    push_notifications: true,
    daily_push_times: ['10:00', '14:00', '17:00'],
    custom_push_settings: {},
    notification_sound: true,
    do_not_disturb_start: '22:00',
    do_not_disturb_end: '08:00',
    favorite_regions: [],
    favorite_categories: []
  });

  useEffect(() => {
    if (!userService.isLoggedIn()) {
      router.push("/login");
      return;
    }
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/user/settings', {
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });
      const data = await response.json();
      if (data.success) {
        setSettings(data.data);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSettingChange = (field: keyof UserSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const addPushTime = () => {
    if (newPushTime && !settings.daily_push_times.includes(newPushTime)) {
      setSettings(prev => ({
        ...prev,
        daily_push_times: [...prev.daily_push_times, newPushTime].sort()
      }));
      setNewPushTime("");
    }
  };

  const removePushTime = (time: string) => {
    setSettings(prev => ({
      ...prev,
      daily_push_times: prev.daily_push_times.filter(t => t !== time)
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/user/settings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      const data = await response.json();
      if (data.success) {
        alert('设置保存成功');
      } else {
        alert('保存失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('保存失败，请重试');
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefault = async () => {
    if (!confirm('确定要重置为默认设置吗？')) {
      return;
    }

    try {
      const response = await fetch('/api/user/settings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${userService.getAccessToken()}`
        }
      });

      const data = await response.json();
      if (data.success) {
        await loadSettings();
        alert('设置已重置为默认值');
      } else {
        alert('重置失败: ' + data.error);
      }
    } catch (error) {
      console.error('Error resetting settings:', error);
      alert('重置失败，请重试');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-700 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:text-white"
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              返回
            </button>
          </div>
          <h1 className="mt-4 text-2xl font-bold text-gray-900 dark:text-white">推送设置</h1>
          <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
            管理您的通知偏好和推送设置
          </p>
        </div>

        <div className="space-y-6">
          {/* Notification Methods */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">通知方式</h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Bell className="w-5 h-5 text-green-600 dark:text-green-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">微信推送</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">通过微信接收推送通知</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={settings.wechat_notifications}
                    onChange={(e) => handleSettingChange('wechat_notifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:bg-gray-800 after:border-gray-300 dark:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Mail className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">邮件通知</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">通过邮件接收通知</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={settings.email_notifications}
                    onChange={(e) => handleSettingChange('email_notifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:bg-gray-800 after:border-gray-300 dark:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Smartphone className="w-5 h-5 text-purple-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">短信通知</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">通过短信接收重要通知</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={settings.sms_notifications}
                    onChange={(e) => handleSettingChange('sms_notifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:bg-gray-800 after:border-gray-300 dark:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Bell className="w-5 h-5 text-orange-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">应用推送</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">通过应用推送接收通知</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={settings.push_notifications}
                    onChange={(e) => handleSettingChange('push_notifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:bg-gray-800 after:border-gray-300 dark:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          {/* Daily Push Settings */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">每日推送</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">上午10点，下午2点和5点分别推送一次信息</p>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Sun className="w-5 h-5 text-yellow-500 mr-3" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">实时推送</span>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={settings.push_notifications}
                    onChange={(e) => handleSettingChange('push_notifications', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:bg-gray-800 after:border-gray-300 dark:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
              
              <div className="text-sm text-gray-600 dark:text-gray-400 pl-8">
                8点-17点实时推送消息
              </div>
            </div>
          </div>

          {/* Custom Push Times */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">自定义推送</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">推送时段 每天</label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <select
                      className="block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      defaultValue=""
                    >
                      <option value="">请选择</option>
                      <option value="morning">上午</option>
                      <option value="afternoon">下午</option>
                      <option value="evening">晚上</option>
                    </select>
                  </div>
                  <div className="flex items-center justify-center">
                    <span className="text-gray-500 dark:text-gray-400">至</span>
                  </div>
                  <div>
                    <select
                      className="block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      defaultValue=""
                    >
                      <option value="">请选择</option>
                      <option value="morning">上午</option>
                      <option value="afternoon">下午</option>
                      <option value="evening">晚上</option>
                    </select>
                  </div>
                </div>
                <div className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  时间间隔 小时推送一次数据
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">推送时间 每天</label>
                <div className="flex space-x-2 mb-4">
                  <input
                    type="time"
                    className="border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                    value={newPushTime}
                    onChange={(e) => setNewPushTime(e.target.value)}
                  />
                  <button
                    type="button"
                    onClick={addPushTime}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
                
                {settings.daily_push_times.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {settings.daily_push_times.map((time) => (
                      <span
                        key={time}
                        className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800"
                      >
                        <Clock className="w-3 h-3 mr-1" />
                        {time}
                        <button
                          type="button"
                          onClick={() => removePushTime(time)}
                          className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-green-600 dark:text-green-400 hover:bg-green-200"
                        >
                          <X className="w-3 h-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
                
                <button
                  type="button"
                  className="mt-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-500"
                >
                  + 新增时间
                </button>
              </div>
            </div>
          </div>

          {/* Other Settings */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">其他设置</h2>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  {settings.notification_sound ? (
                    <Volume2 className="w-5 h-5 text-blue-600 dark:text-blue-400 mr-3" />
                  ) : (
                    <VolumeX className="w-5 h-5 text-gray-400 mr-3" />
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">通知声音</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">接收通知时播放声音</p>
                  </div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={settings.notification_sound}
                    onChange={(e) => handleSettingChange('notification_sound', e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:bg-gray-800 after:border-gray-300 dark:border-gray-600 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <div>
                <div className="flex items-center mb-3">
                  <Moon className="w-5 h-5 text-indigo-600 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">免打扰时间</p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">在指定时间段内不接收通知</p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 pl-8">
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">开始时间</label>
                    <input
                      type="time"
                      className="block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      value={settings.do_not_disturb_start}
                      onChange={(e) => handleSettingChange('do_not_disturb_start', e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">结束时间</label>
                    <input
                      type="time"
                      className="block w-full border border-gray-300 dark:border-gray-600 rounded-md shadow-sm py-2 px-3 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      value={settings.do_not_disturb_end}
                      onChange={(e) => handleSettingChange('do_not_disturb_end', e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <button
              type="button"
              onClick={resetToDefault}
              className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:bg-gray-700"
            >
              重置默认
            </button>
            
            <button
              type="button"
              onClick={handleSave}
              disabled={isSaving}
              className="inline-flex items-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-400"
            >
              {isSaving ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {isSaving ? '保存中...' : '保存设置'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
