"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown } from "lucide-react";
import Link from "next/link";

interface RegionOption {
  id: string;
  name: string;
  subOptions?: string[];
}

interface RegionSelectorProps {
  options: RegionOption[];
  selectedRegion?: string;
  selectedProvinces?: string[];
  baseUrl: string;
  onApply?: (region: string, provinces: string[]) => void;
}

export default function RegionSelector({
  options,
  selectedRegion = "all",
  selectedProvinces = [],
  baseUrl,
  onApply,
}: RegionSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [localSelectedRegion, setLocalSelectedRegion] = useState(selectedRegion);
  const [localSelectedProvinces, setLocalSelectedProvinces] = useState<string[]>(selectedProvinces);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 当props变化时更新本地状态
  useEffect(() => {
    setLocalSelectedRegion(selectedRegion);
    setLocalSelectedProvinces(selectedProvinces);
  }, [selectedRegion, selectedProvinces]);

  // 关闭下拉菜单当点击外部区域
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // 计算显示文本
  const getDisplayText = () => {
    if (localSelectedRegion === "all") return "全国";

    const region = options.find(r => r.id === localSelectedRegion);
    if (region) return region.name;

    if (localSelectedProvinces.length > 0) {
      return localSelectedProvinces.length > 1
        ? `已选${localSelectedProvinces.length}个省份`
        : localSelectedProvinces[0];
    }

    return "选择地区";
  };

  // 切换地区选择
  const toggleRegion = (regionId: string) => {
    if (regionId === "all") {
      setLocalSelectedRegion("all");
      setLocalSelectedProvinces([]);
    } else if (localSelectedRegion === regionId) {
      setLocalSelectedRegion("");
    } else {
      setLocalSelectedRegion(regionId);
    }
  };

  // 切换省份选择
  const toggleProvince = (province: string) => {
    if (localSelectedProvinces.includes(province)) {
      setLocalSelectedProvinces(localSelectedProvinces.filter(p => p !== province));
    } else {
      setLocalSelectedProvinces([...localSelectedProvinces, province]);
    }

    // 如果选中了省份，清除“全国”选项
    if (localSelectedRegion === "all") {
      setLocalSelectedRegion("");
    }
  };

  // 生成URL
  const generateUrl = () => {
    const url = new URL(baseUrl, 'http://example.com');

    // 添加地区参数
    if (localSelectedRegion && localSelectedRegion !== "all") {
      url.searchParams.set('region', localSelectedRegion);
    }

    // 添加省份参数
    localSelectedProvinces.forEach(province => {
      url.searchParams.append('province', province);
    });

    return url.pathname + url.search;
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="px-3 py-1 text-sm border text-blue-500 hover:bg-gray-50 flex items-center"
      >
        {getDisplayText()}
        <ChevronDown className="h-4 w-4 ml-1" />
      </button>

      {isOpen && (
        <div className="absolute left-0 mt-1 w-96 bg-white border rounded shadow-lg z-50">
          <div className="p-3">
            <div className="mb-2 border-b pb-2">
              <div className="flex justify-between items-center">
                <h3 className="font-medium">选择地区</h3>
                <button
                  onClick={() => setIsOpen(false)}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  关闭
                </button>
              </div>
            </div>

            <div className="space-y-3">
              {options.map((region) => (
                <div key={region.id} className="border-b pb-2 last:border-b-0">
                  <div className="flex items-center mb-1">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        className="mr-2"
                        checked={localSelectedRegion === region.id}
                        onChange={() => toggleRegion(region.id)}
                      />
                      <span className={`text-sm font-medium ${localSelectedRegion === region.id
                          ? "text-blue-500"
                          : "text-gray-700"
                        }`}>
                        {region.name}
                      </span>
                    </label>
                  </div>

                  {region.subOptions && (
                    <div className="flex flex-wrap gap-2 ml-4">
                      {region.subOptions.map((province, idx) => (
                        <label key={idx} className="flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="mr-1"
                            checked={localSelectedProvinces.includes(province)}
                            onChange={() => toggleProvince(province)}
                          />
                          <span className={`text-sm ${localSelectedProvinces.includes(province)
                              ? "text-blue-500"
                              : "text-gray-600"
                            }`}>
                            {province}
                          </span>
                        </label>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end p-3 bg-gray-50 rounded-b">
            <button
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
              onClick={() => {
                // 如果提供了onApply回调，则调用它
                if (onApply) {
                  onApply(localSelectedRegion, localSelectedProvinces);
                }
                setIsOpen(false);
              }}
            >
              确定
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
