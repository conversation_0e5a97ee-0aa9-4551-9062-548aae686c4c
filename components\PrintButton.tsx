"use client";

import { Printer } from "lucide-react";
import { useState } from "react";

interface PrintButtonProps {
  className?: string;
  variant?: "default" | "outline" | "ghost";
  size?: "sm" | "md" | "lg";
  printTitle?: string;
}

export default function PrintButton({
  className = "",
  variant = "default",
  size = "md",
  printTitle = "打印页面"
}: PrintButtonProps) {
  const [isPrinting, setIsPrinting] = useState(false);

  const handlePrint = async () => {
    setIsPrinting(true);
    
    try {
      // 添加打印样式
      const printStyles = `
        <style>
          @media print {
            /* 隐藏不需要打印的元素 */
            .no-print,
            nav,
            header,
            footer,
            .print-button,
            button:not(.print-keep),
            .sidebar,
            .navigation {
              display: none !important;
            }
            
            /* 打印页面样式 */
            body {
              font-size: 12pt;
              line-height: 1.4;
              color: #000;
              background: #fff;
            }
            
            /* 确保内容适合页面 */
            .max-w-7xl {
              max-width: none !important;
              margin: 0 !important;
              padding: 0 !important;
            }
            
            /* 表格样式 */
            table {
              border-collapse: collapse;
              width: 100%;
              margin-bottom: 20px;
            }
            
            table, th, td {
              border: 1px solid #000;
            }
            
            th, td {
              padding: 8px;
              text-align: left;
            }
            
            th {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            
            /* 标题样式 */
            h1, h2, h3, h4, h5, h6 {
              color: #000;
              margin-bottom: 10px;
              page-break-after: avoid;
            }
            
            /* 避免在不合适的地方分页 */
            .avoid-break {
              page-break-inside: avoid;
            }
            
            /* 链接样式 */
            a {
              color: #000;
              text-decoration: none;
            }
            
            a:after {
              content: " (" attr(href) ")";
              font-size: 10pt;
              color: #666;
            }
            
            /* 文件列表样式 */
            .file-list {
              margin-bottom: 20px;
            }
            
            .file-item {
              border-bottom: 1px solid #ddd;
              padding: 10px 0;
            }
            
            /* 页眉页脚 */
            @page {
              margin: 2cm;
              @top-center {
                content: "${printTitle}";
                font-size: 14pt;
                font-weight: bold;
              }
              @bottom-center {
                content: "第 " counter(page) " 页";
                font-size: 10pt;
              }
            }
            
            /* 深色模式适配 */
            .dark * {
              color: #000 !important;
              background: #fff !important;
              border-color: #000 !important;
            }
          }
        </style>
      `;
      
      // 将打印样式添加到页面
      const styleElement = document.createElement('div');
      styleElement.innerHTML = printStyles;
      document.head.appendChild(styleElement);
      
      // 添加打印时间戳
      const timestamp = new Date().toLocaleString('zh-CN');
      const timestampElement = document.createElement('div');
      timestampElement.className = 'print-timestamp no-print';
      timestampElement.style.cssText = 'display: none;';
      timestampElement.innerHTML = `
        <div style="text-align: right; font-size: 10pt; color: #666; margin-bottom: 20px;">
          打印时间: ${timestamp}
        </div>
      `;
      
      // 在打印时显示时间戳
      const printTimestampStyle = document.createElement('style');
      printTimestampStyle.innerHTML = `
        @media print {
          .print-timestamp {
            display: block !important;
          }
        }
      `;
      document.head.appendChild(printTimestampStyle);
      
      // 将时间戳插入到打印内容区域顶部
      const printContent = document.querySelector('.print-content-area') || document.querySelector('.max-w-7xl');
      if (printContent) {
        printContent.insertBefore(timestampElement, printContent.firstChild);
      }
      
      // 等待样式应用
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // 执行打印
      window.print();
      
      // 清理添加的元素
      setTimeout(() => {
        document.head.removeChild(styleElement);
        document.head.removeChild(printTimestampStyle);
        if (timestampElement.parentNode) {
          timestampElement.parentNode.removeChild(timestampElement);
        }
      }, 1000);
      
    } catch (error) {
      console.error('打印失败:', error);
      alert('打印失败，请重试');
    } finally {
      setIsPrinting(false);
    }
  };

  const getButtonClasses = () => {
    const baseClasses = "inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none";
    
    const sizeClasses = {
      sm: "h-8 px-3 text-sm",
      md: "h-10 px-4 text-sm",
      lg: "h-12 px-6 text-base"
    };
    
    const variantClasses = {
      default: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500",
      outline: "border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-blue-500",
      ghost: "text-gray-700 hover:bg-gray-100 focus:ring-blue-500"
    };
    
    return `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;
  };

  return (
    <button
      onClick={handlePrint}
      disabled={isPrinting}
      className={`print-button ${getButtonClasses()}`}
      title="打印页面"
    >
      <Printer className={`${size === 'sm' ? 'h-4 w-4' : size === 'lg' ? 'h-6 w-6' : 'h-5 w-5'} ${isPrinting ? 'animate-pulse' : ''}`} />
      <span className="ml-2">
        {isPrinting ? '准备打印...' : '打印'}
      </span>
    </button>
  );
}
