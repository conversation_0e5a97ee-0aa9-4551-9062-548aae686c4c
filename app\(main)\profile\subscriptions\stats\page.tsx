"use client";

import { useState, useEffect } from "react";
import userService from "@/lib/userService";
import { 
  BarChart3, 
  TrendingUp, 
  Calendar,
  Target,
  Bell,
  Users,
  Clock,
  Award
} from "lucide-react";

interface SubscriptionStats {
  total_subscriptions: number;
  active_subscriptions: number;
  total_matches: number;
  total_pushes: number;
  avg_matches_per_subscription: number;
  most_active_subscription: {
    name: string;
    match_count: number;
  } | null;
  recent_activity: {
    date: string;
    matches: number;
    pushes: number;
  }[];
  subscription_performance: {
    subscription_name: string;
    match_count: number;
    push_count: number;
    success_rate: number;
  }[];
}

export default function SubscriptionStatsPage() {
  const [stats, setStats] = useState<SubscriptionStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState(30); // days

  useEffect(() => {
    loadStats();
  }, [timeRange]);

  const loadStats = async () => {
    setIsLoading(true);
    try {
      // 这里应该调用实际的统计API
      // 现在使用模拟数据
      const mockStats: SubscriptionStats = {
        total_subscriptions: 5,
        active_subscriptions: 4,
        total_matches: 128,
        total_pushes: 96,
        avg_matches_per_subscription: 25.6,
        most_active_subscription: {
          name: "建筑工程招标",
          match_count: 45
        },
        recent_activity: [
          { date: "2024-01-15", matches: 8, pushes: 6 },
          { date: "2024-01-14", matches: 12, pushes: 9 },
          { date: "2024-01-13", matches: 6, pushes: 4 },
          { date: "2024-01-12", matches: 15, pushes: 12 },
          { date: "2024-01-11", matches: 9, pushes: 7 },
          { date: "2024-01-10", matches: 11, pushes: 8 },
          { date: "2024-01-09", matches: 7, pushes: 5 }
        ],
        subscription_performance: [
          { subscription_name: "建筑工程招标", match_count: 45, push_count: 38, success_rate: 84.4 },
          { subscription_name: "IT设备采购", match_count: 32, push_count: 28, success_rate: 87.5 },
          { subscription_name: "医疗器械招标", match_count: 28, push_count: 22, success_rate: 78.6 },
          { subscription_name: "环保项目", match_count: 23, push_count: 8, success_rate: 34.8 }
        ]
      };

      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 1000));
      setStats(mockStats);
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">加载统计数据中...</p>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="text-center py-12">
          <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">暂无统计数据</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">请先创建订阅以查看统计信息</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">订阅统计</h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              查看您的订阅表现和匹配统计
            </p>
          </div>
          <select
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            value={timeRange}
            onChange={(e) => setTimeRange(parseInt(e.target.value))}
          >
            <option value={7}>最近7天</option>
            <option value={30}>最近30天</option>
            <option value={90}>最近90天</option>
          </select>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">总订阅数</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stats.total_subscriptions}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
            <div className="text-sm">
              <span className="text-green-600 dark:text-green-400 font-medium">{stats.active_subscriptions}</span>
              <span className="text-gray-500 dark:text-gray-400"> 个活跃</span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">总匹配数</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stats.total_matches}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
            <div className="text-sm">
              <span className="text-blue-600 dark:text-blue-400 font-medium">{stats.avg_matches_per_subscription.toFixed(1)}</span>
              <span className="text-gray-500 dark:text-gray-400"> 平均每订阅</span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Bell className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">总推送数</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white">{stats.total_pushes}</dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
            <div className="text-sm">
              <span className="text-purple-600 font-medium">
                {((stats.total_pushes / stats.total_matches) * 100).toFixed(1)}%
              </span>
              <span className="text-gray-500 dark:text-gray-400"> 推送率</span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Award className="h-6 w-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">最佳订阅</dt>
                  <dd className="text-lg font-medium text-gray-900 dark:text-white truncate">
                    {stats.most_active_subscription?.name || '暂无'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 dark:bg-gray-700 px-5 py-3">
            <div className="text-sm">
              <span className="text-yellow-600 dark:text-yellow-400 font-medium">
                {stats.most_active_subscription?.match_count || 0}
              </span>
              <span className="text-gray-500 dark:text-gray-400"> 次匹配</span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity Chart */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg mb-8">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">最近活动趋势</h2>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {stats.recent_activity.map((activity, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <span className="text-sm text-gray-900 dark:text-white">
                    {new Date(activity.date).toLocaleDateString('zh-CN')}
                  </span>
                </div>
                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">匹配: {activity.matches}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span className="text-sm text-gray-600 dark:text-gray-400">推送: {activity.pushes}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Subscription Performance */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">订阅表现</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  订阅名称
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  匹配数
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  推送数
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  成功率
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  表现
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200">
              {stats.subscription_performance.map((subscription, index) => (
                <tr key={index}>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {subscription.subscription_name}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {subscription.match_count}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {subscription.push_count}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                    {subscription.success_rate.toFixed(1)}%
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div 
                          className={`h-2 rounded-full ${
                            subscription.success_rate >= 80 ? 'bg-green-500' :
                            subscription.success_rate >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${subscription.success_rate}%` }}
                        ></div>
                      </div>
                      <span className={`text-xs font-medium ${
                        subscription.success_rate >= 80 ? 'text-green-600 dark:text-green-400' :
                        subscription.success_rate >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {subscription.success_rate >= 80 ? '优秀' :
                         subscription.success_rate >= 60 ? '良好' : '需改进'}
                      </span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
