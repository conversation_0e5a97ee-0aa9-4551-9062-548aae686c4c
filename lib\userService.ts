import passwordCrypto from "@/lib/passwordCrypto";

interface RegisterData {
  company_name: string;
  contact_name: string;
  phone: string;
  password: string;
  email?: string;
  address?: string;
}

interface LoginData {
  phone: string;
  password: string;
}

interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  detail?: string;
}

interface UserData {
  id: number;
  company_name: string;
  contact_name: string;
  phone: string;
  email?: string;
  address?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  updated_at: string;
  last_login?: string;
}

interface LoginResponse {
  user: UserData;
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

class UserService {
  private baseURL: string;
  private accessToken: string | null;
  private refreshToken: string | null;

  constructor() {
    this.baseURL =
      process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000";
    this.accessToken = null;
    this.refreshToken = null;

    // 如果在浏览器环境，从localStorage恢复token
    if (typeof window !== "undefined") {
      this.accessToken = localStorage.getItem("access_token");
      this.refreshToken = localStorage.getItem("refresh_token");
    }
  }

  // 用户注册
  async register(userData: RegisterData): Promise<ApiResponse<UserData>> {
    try {
      // 加密密码
      const encryptedData = {
        ...userData,
        password: passwordCrypto.encrypt(userData.password),
      };

      const response = await fetch(`${this.baseURL}/api/user/register`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(encryptedData),
      });

      const result: ApiResponse<UserData> = await response.json();

      if (!response.ok) {
        throw new Error(result.detail || "注册失败");
      }

      return result;
    } catch (error) {
      console.error("注册错误:", error);
      throw error;
    }
  }

  // 用户登录
  async login(
    phone: string,
    password: string
  ): Promise<ApiResponse<LoginResponse>> {
    try {
      // 加密密码
      const encryptedPassword = passwordCrypto.encrypt(password);

      const response = await fetch(`${this.baseURL}/api/user/login`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phone,
          password: encryptedPassword,
        }),
      });

      const result: ApiResponse<LoginResponse> = await response.json();

      if (!response.ok) {
        throw new Error(result.detail || "登录失败");
      }

      if (result.success && result.data) {
        // 保存token
        this.accessToken = result.data.access_token;
        this.refreshToken = result.data.refresh_token;

        if (typeof window !== "undefined") {
          localStorage.setItem("access_token", this.accessToken);
          localStorage.setItem("refresh_token", this.refreshToken);
          // 也保存用户信息
          localStorage.setItem("user_info", JSON.stringify(result.data.user));
        }
      }

      return result;
    } catch (error) {
      console.error("登录错误:", error);
      throw error;
    }
  }

  // 获取用户资料
  async getProfile(): Promise<ApiResponse<UserData>> {
    const response = await this.authRequest("/api/user/profile");
    return await response.json();
  }

  // 更新用户资料
  async updateProfile(
    userData: Partial<UserData>
  ): Promise<ApiResponse<UserData>> {
    const response = await this.authRequest("/api/user/profile", {
      method: "PUT",
      body: JSON.stringify(userData),
    });
    return await response.json();
  }

  // 认证请求（自动处理token刷新）
  async authRequest(url: string, options: RequestInit = {}): Promise<Response> {
    const headers = {
      Authorization: `Bearer ${this.accessToken}`,
      "Content-Type": "application/json",
      ...options.headers,
    };

    let response = await fetch(`${this.baseURL}${url}`, {
      ...options,
      headers,
    });

    // 如果token过期，尝试刷新
    if (response.status === 401 && this.refreshToken) {
      const refreshed = await this.refreshAccessToken();
      if (refreshed) {
        // 重新发送原始请求
        headers["Authorization"] = `Bearer ${this.accessToken}`;
        response = await fetch(`${this.baseURL}${url}`, {
          ...options,
          headers,
        });
      }
    }

    return response;
  }

  // 刷新访问令牌
  async refreshAccessToken(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/api/user/refresh`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          refresh_token: this.refreshToken,
        }),
      });

      const result: ApiResponse<{
        access_token: string;
        token_type: string;
        expires_in: number;
      }> = await response.json();

      if (result.success && result.data) {
        this.accessToken = result.data.access_token;
        if (typeof window !== "undefined") {
          localStorage.setItem("access_token", this.accessToken);
        }
        return true;
      }
    } catch (error) {
      console.error("Token refresh failed:", error);
    }

    // 刷新失败，清除token
    this.logout();
    return false;
  }

  // 登出
  logout() {
    this.accessToken = null;
    this.refreshToken = null;

    if (typeof window !== "undefined") {
      localStorage.removeItem("access_token");
      localStorage.removeItem("refresh_token");
      localStorage.removeItem("user_info");
    }
  }

  // 检查是否已登录
  isLoggedIn(): boolean {
    return !!this.accessToken;
  }

  // 获取当前用户信息（从localStorage）
  getCurrentUser(): UserData | null {
    if (typeof window !== "undefined") {
      const userInfo = localStorage.getItem("user_info");
      return userInfo ? JSON.parse(userInfo) : null;
    }
    return null;
  }

  // 获取当前access token
  getAccessToken(): string | null {
    return this.accessToken;
  }
}

// 创建单例实例
const userService = new UserService();

export default userService;
export type { RegisterData, LoginData, UserData, ApiResponse, LoginResponse };
