"use client";

import { useState, useEffect } from "react";
import userService, { type UserData } from "@/lib/userService";
import { 
  Crown, 
  Check, 
  X, 
  Star,
  Zap,
  Shield,
  Bell,
  Mail,
  Smartphone,
  BarChart3,
  Clock,
  Users,
  Headphones
} from "lucide-react";
import React from "react";

interface MembershipPlan {
  id: string;
  name: string;
  price: string;
  period: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  borderColor: string;
  features: {
    category: string;
    items: {
      name: string;
      free: boolean | string;
      vip: boolean | string;
      premium: boolean | string;
    }[];
  }[];
  highlights: string[];
}

const membershipPlans: MembershipPlan[] = [
  {
    id: "free",
    name: "免费会员",
    price: "¥0",
    period: "永久免费",
    icon: <Users className="w-6 h-6" />,
    color: "text-gray-600 dark:text-gray-400",
    bgColor: "bg-gray-50 dark:bg-gray-700",
    borderColor: "border-gray-200 dark:border-gray-700",
    features: [],
    highlights: [
      "基础信息查看",
      "有限订阅数量",
      "标准推送服务"
    ]
  },
  {
    id: "vip",
    name: "VIP会员",
    price: "¥99",
    period: "每月",
    icon: <Crown className="w-6 h-6" />,
    color: "text-yellow-600 dark:text-yellow-400",
    bgColor: "bg-yellow-50 dark:bg-yellow-900/20",
    borderColor: "border-yellow-200",
    features: [],
    highlights: [
      "无限订阅数量",
      "实时推送通知",
      "高级筛选功能",
      "优先客服支持"
    ]
  },
  {
    id: "premium",
    name: "高级会员",
    price: "¥199",
    period: "每月",
    icon: <Star className="w-6 h-6" />,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200",
    features: [],
    highlights: [
      "VIP所有功能",
      "数据分析报告",
      "API接口访问",
      "专属客户经理",
      "定制化服务"
    ]
  }
];

const featureCategories = [
  {
    category: "订阅功能",
    icon: <Bell className="w-5 h-5" />,
    items: [
      { name: "订阅数量", free: "3个", vip: "无限", premium: "无限" },
      { name: "关键词数量", free: "5个/订阅", vip: "10个/订阅", premium: "20个/订阅" },
      { name: "地区选择", free: true, vip: true, premium: true },
      { name: "金额筛选", free: false, vip: true, premium: true },
      { name: "高级筛选", free: false, vip: true, premium: true }
    ]
  },
  {
    category: "推送服务",
    icon: <Zap className="w-5 h-5" />,
    items: [
      { name: "邮件推送", free: true, vip: true, premium: true },
      { name: "微信推送", free: false, vip: true, premium: true },
      { name: "短信推送", free: false, vip: true, premium: true },
      { name: "实时推送", free: false, vip: true, premium: true },
      { name: "推送频率", free: "每日1次", vip: "每日3次", premium: "实时推送" }
    ]
  },
  {
    category: "数据分析",
    icon: <BarChart3 className="w-5 h-5" />,
    items: [
      { name: "基础统计", free: true, vip: true, premium: true },
      { name: "趋势分析", free: false, vip: true, premium: true },
      { name: "竞争分析", free: false, vip: false, premium: true },
      { name: "数据导出", free: false, vip: "Excel", premium: "Excel/PDF/API" },
      { name: "历史数据", free: "30天", vip: "1年", premium: "无限" }
    ]
  },
  {
    category: "客户服务",
    icon: <Headphones className="w-5 h-5" />,
    items: [
      { name: "在线客服", free: "工作时间", vip: "优先响应", premium: "7×24小时" },
      { name: "技术支持", free: "基础", vip: "高级", premium: "专属" },
      { name: "培训服务", free: false, vip: "在线培训", premium: "一对一培训" },
      { name: "定制服务", free: false, vip: false, premium: true }
    ]
  }
];

export default function MembershipPage() {
  const [user, setUser] = useState<UserData | null>(null);
  const [currentPlan, setCurrentPlan] = useState<string>("free");

  useEffect(() => {
    const currentUser = userService.getCurrentUser();
    if (currentUser) {
      setUser(currentUser);
      setCurrentPlan((currentUser as any).membership_type || "free");
    }
  }, []);

  const renderFeatureValue = (value: boolean | string) => {
    if (typeof value === "boolean") {
      return value ? (
        <Check className="w-4 h-4 text-green-500" />
      ) : (
        <X className="w-4 h-4 text-gray-400" />
      );
    }
    return <span className="text-sm text-gray-700 dark:text-gray-300">{value}</span>;
  };

  const handleUpgrade = (planId: string) => {
    // 这里可以集成支付系统
    alert(`升级到${membershipPlans.find(p => p.id === planId)?.name}功能开发中...`);
  };

  return (
    <div className="max-w-6xl mx-auto">
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">会员权益</h1>
        <p className="mt-2 text-gray-600 dark:text-gray-400">
          选择适合您的会员计划，享受更多专业功能和服务
        </p>
      </div>

      {/* 当前会员状态 */}
      {user && (
        <div className="mb-8 p-6 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Crown className="w-6 h-6 text-blue-600 dark:text-blue-400" />
              <div>
                <h3 className="text-lg font-medium text-blue-900">
                  当前会员：{membershipPlans.find(p => p.id === currentPlan)?.name}
                </h3>
                <p className="text-blue-700">
                  {currentPlan === "free" ? "享受基础功能" : "感谢您的支持！"}
                </p>
              </div>
            </div>
            {currentPlan === "free" && (
              <button
                onClick={() => handleUpgrade("vip")}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                立即升级
              </button>
            )}
          </div>
        </div>
      )}

      {/* 会员计划对比 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        {membershipPlans.map((plan) => (
          <div
            key={plan.id}
            className={`relative p-6 rounded-lg border-2 ${plan.borderColor} ${plan.bgColor} ${
              currentPlan === plan.id ? "ring-2 ring-blue-500" : ""
            }`}
          >
            {currentPlan === plan.id && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  当前计划
                </span>
              </div>
            )}
            
            <div className="text-center mb-6">
              <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full ${plan.bgColor} ${plan.color} mb-4`}>
                {plan.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white">{plan.name}</h3>
              <div className="mt-2">
                <span className="text-3xl font-bold text-gray-900 dark:text-white">{plan.price}</span>
                <span className="text-gray-600 dark:text-gray-400">/{plan.period}</span>
              </div>
            </div>

            <ul className="space-y-3 mb-6">
              {plan.highlights.map((highlight, index) => (
                <li key={index} className="flex items-center">
                  <Check className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{highlight}</span>
                </li>
              ))}
            </ul>

            <button
              onClick={() => handleUpgrade(plan.id)}
              disabled={currentPlan === plan.id}
              className={`w-full py-2 px-4 rounded-md font-medium transition-colors ${
                currentPlan === plan.id
                  ? "bg-gray-200 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  : plan.id === "free"
                  ? "bg-gray-600 text-white hover:bg-gray-700"
                  : plan.id === "vip"
                  ? "bg-yellow-600 text-white hover:bg-yellow-700"
                  : "bg-purple-600 text-white hover:bg-purple-700"
              }`}
            >
              {currentPlan === plan.id ? "当前计划" : 
               plan.id === "free" ? "当前使用" : "立即升级"}
            </button>
          </div>
        ))}
      </div>

      {/* 详细功能对比表 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">功能对比详情</h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  功能
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  免费会员
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  VIP会员
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                  高级会员
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200">
              {featureCategories.map((category) => (
                <React.Fragment key={category.category}>
                  <tr className="bg-gray-50 dark:bg-gray-700">
                    <td colSpan={4} className="px-6 py-3">
                      <div className="flex items-center space-x-2">
                        {category.icon}
                        <span className="font-medium text-gray-900 dark:text-white">{category.category}</span>
                      </div>
                    </td>
                  </tr>
                  {category.items.map((item, index) => (
                    <tr key={index}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                        {item.name}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        {renderFeatureValue(item.free)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        {renderFeatureValue(item.vip)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-center">
                        {renderFeatureValue(item.premium)}
                      </td>
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 常见问题 */}
      <div className="mt-12">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-6">常见问题</h2>
        <div className="space-y-4">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">如何升级会员？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              点击上方的&ldquo;立即升级&rdquo;按钮，选择适合的会员计划，完成支付即可立即享受会员权益。
            </p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">会员费用如何计算？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              会员费用按月计算，支持月付、季付、年付等多种方式。年付用户可享受额外优惠。
            </p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">可以随时取消会员吗？</h3>
            <p className="text-gray-600 dark:text-gray-400">
              可以随时取消会员订阅，取消后在当前计费周期结束前仍可享受会员权益。
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
