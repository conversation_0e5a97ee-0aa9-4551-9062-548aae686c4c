import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import userService from '@/lib/userService';

// GET - 获取用户下载记录
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    let userData;
    
    try {
      // 这里应该实现JWT token验证逻辑
      // userData = jwt.verify(token, process.env.JWT_SECRET);
      // 暂时返回模拟数据
      userData = { id: 1 };
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // 获取用户的下载记录
    const query = `
      SELECT 
        id, notice_id, notice_title, notice_type, file_name, 
        file_size, file_url, download_time, site_name
      FROM user_downloads 
      WHERE user_id = ?
      ORDER BY download_time DESC
      LIMIT ? OFFSET ?
    `;
    
    const downloads = await executeQuery(query, [userData.id, limit, offset]);

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM user_downloads 
      WHERE user_id = ?
    `;
    const countResult = await executeQuery(countQuery, [userData.id]) as any[];
    const total = countResult[0].total;

    return NextResponse.json({
      downloads,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Failed to fetch user downloads:', error);
    return NextResponse.json(
      { error: 'Failed to fetch downloads' },
      { status: 500 }
    );
  }
}

// POST - 记录下载
export async function POST(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    let userData;
    
    try {
      // 这里应该实现JWT token验证逻辑
      userData = { id: 1 };
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      notice_id, 
      notice_title, 
      notice_type, 
      file_name, 
      file_size, 
      file_url, 
      site_name 
    } = body;

    // 验证必填字段
    if (!notice_id || !file_name || !file_url) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // 插入下载记录
    const insertQuery = `
      INSERT INTO user_downloads (
        user_id, notice_id, notice_title, notice_type, 
        file_name, file_size, file_url, site_name, download_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    const result = await executeQuery(insertQuery, [
      userData.id,
      notice_id,
      notice_title || '',
      notice_type || '',
      file_name,
      file_size || '',
      file_url,
      site_name || ''
    ]);

    return NextResponse.json({
      message: 'Download recorded successfully',
      id: (result as any).insertId
    });
  } catch (error) {
    console.error('Failed to record download:', error);
    return NextResponse.json(
      { error: 'Failed to record download' },
      { status: 500 }
    );
  }
}

// DELETE - 删除下载记录
export async function DELETE(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    let userData;
    
    try {
      userData = { id: 1 };
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const downloadId = searchParams.get('id');

    if (!downloadId) {
      return NextResponse.json(
        { error: 'Download ID required' },
        { status: 400 }
      );
    }

    // 删除下载记录（只能删除自己的记录）
    const deleteQuery = `
      DELETE FROM user_downloads 
      WHERE id = ? AND user_id = ?
    `;

    await executeQuery(deleteQuery, [parseInt(downloadId), userData.id]);

    return NextResponse.json({
      message: 'Download record deleted successfully'
    });
  } catch (error) {
    console.error('Failed to delete download record:', error);
    return NextResponse.json(
      { error: 'Failed to delete download record' },
      { status: 500 }
    );
  }
}
