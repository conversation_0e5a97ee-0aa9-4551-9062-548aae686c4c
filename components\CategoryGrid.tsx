import Link from "next/link";
import {
  FileText,
  MapPin,
  Newspaper,
  Settings,
  Briefcase,
  Building,
  BadgeCheck,
  Zap,
  ChevronRight,
  FileEdit,
  CreditCard,
  Bell,
} from "lucide-react";

interface CategoryItem {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  color: string;
  href: string;
}

const categories: CategoryItem[] = [
  {
    title: "招标预告",
    subtitle: "招标预告 招标计划",
    icon: <FileText className="h-8 w-8 fill-yellow-600" />,
    color: "bg-yellow-100",
    href: "/notices",
  },
  {
    title: "招标公告",
    subtitle: "最新公告 精准推送",
    icon: <Newspaper className="h-8 w-8 fill-cyan-600" />,
    color: "bg-cyan-100",
    href: "/category/document",
  },
  {
    title: "招标变更",
    subtitle: "招标变更 精准推送",
    icon: <Settings className="h-8 w-8 fill-violet-600" />,
    color: "bg-violet-100",
    href: "/category/change",
  },
  {
    title: "中标结果",
    subtitle: "中标结果 精准推送",
    icon: <BadgeCheck className="h-8 w-8 fill-blue-600" />,
    color: "bg-blue-100",
    href: "/category/result",
  },
  {
    title: "招建项目",
    subtitle: "招建项目 最新分类",
    icon: <Briefcase className="h-8 w-8 fill-red-600" />,
    color: "bg-red-100",
    href: "/category/project",
  },
  {
    title: "信息定制",
    subtitle: "最新公告 精准推送",
    icon: <FileEdit className="h-8 w-8 fill-amber-600" />,
    color: "bg-amber-100",
    href: "/category/custom",
  },
  {
    title: "发布招标",
    subtitle: "发布招标 精准推送",
    icon: <Bell className="h-8 w-8 fill-rose-600" />,
    color: "bg-rose-100",
    href: "/category/publish",
  },
  {
    title: "发布中标",
    subtitle: "发布中标 精准推送",
    icon: <CreditCard className="h-8 w-8 fill-orange-600" />,
    color: "bg-orange-100",
    href: "/category/publish-result",
  },
];

export default function CategoryGrid() {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 my-8">
      {categories.map((category) => (
        <Link
          key={category.title}
          href={category.href}
          className="group block bg-white dark:bg-gray-800 border border-gray-100 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-all flex items-start"
        >
          <div className={`${category.color} p-3 rounded-lg mr-3`}>
            {category.icon}
          </div>
          <div className="flex-grow">
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors flex items-center">
              {category.title}
              <ChevronRight className="h-4 w-4 ml-1 inline-block opacity-0 group-hover:opacity-100 transition-opacity" />
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {category.subtitle}
            </p>
          </div>
        </Link>
      ))}
    </div>
  );
}
