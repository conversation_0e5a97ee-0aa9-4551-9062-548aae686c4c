import { NextRequest, NextResponse } from 'next/server';
import { executeQuery } from '@/lib/db';
import userService from '@/lib/userService';

// GET - 获取当前用户的反馈列表
export async function GET(request: NextRequest) {
  try {
    // 验证用户身份
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Authorization header required' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    let userData;

    try {
      // 这里应该实现JWT token验证逻辑
      // userData = jwt.verify(token, process.env.JWT_SECRET);
      // 暂时返回模拟数据
      userData = { id: 1 };
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid token' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    // 获取用户的反馈列表
    const query = `
      SELECT 
        id, type, title, content, rating, status, admin_reply, 
        created_at, updated_at
      FROM user_feedback 
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const feedbacks = await executeQuery(query, [userData.id, limit, offset]);

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total 
      FROM user_feedback 
      WHERE user_id = ?
    `;
    const countResult = await executeQuery(countQuery, [userData.id]) as any[];
    const total = countResult[0].total;

    return NextResponse.json({
      feedbacks,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Failed to fetch user feedbacks:', error);
    return NextResponse.json(
      { error: 'Failed to fetch feedbacks' },
      { status: 500 }
    );
  }
}
