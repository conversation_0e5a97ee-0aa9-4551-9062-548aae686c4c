-- Database Schema Updates for Enhanced Subscription and Notification Features
-- Execute these SQL statements to update the existing database structure

-- 1. Add new columns to user_subscriptions table for enhanced functionality
ALTER TABLE user_subscriptions 
ADD COLUMN subscription_type ENUM('tender_announcement', 'winning_announcement', 'both') DEFAULT 'both' COMMENT '订阅类型：招标公告、中标公告或两者',
ADD COLUMN wechat_push TINYINT(1) DEFAULT 1 COMMENT '微信推送开关',
ADD COLUMN email_push TINYINT(1) DEFAULT 1 COMMENT '邮件推送开关',
ADD COLUMN push_frequency ENUM('realtime', 'daily', 'custom') DEFAULT 'daily' COMMENT '推送频率',
ADD COLUMN push_times JSON COMMENT '自定义推送时间点',
ADD COLUMN daily_push_enabled TINYINT(1) DEFAULT 1 COMMENT '每日推送开关',
ADD COLUMN realtime_push_enabled TINYINT(1) DEFAULT 0 COMMENT '实时推送开关',
ADD COLUMN realtime_push_start_time TIME DEFAULT '08:00:00' COMMENT '实时推送开始时间',
ADD COLUMN realtime_push_end_time TIME DEFAULT '17:00:00' COMMENT '实时推送结束时间';

-- 2. Enhance user_settings table for comprehensive notification settings
ALTER TABLE user_settings 
ADD COLUMN wechat_notifications TINYINT(1) DEFAULT 1 COMMENT '微信通知开关',
ADD COLUMN push_notifications TINYINT(1) DEFAULT 1 COMMENT '推送通知开关',
ADD COLUMN daily_push_times JSON DEFAULT '["10:00", "14:00", "17:00"]' COMMENT '每日推送时间点',
ADD COLUMN custom_push_settings JSON COMMENT '自定义推送设置',
ADD COLUMN notification_sound TINYINT(1) DEFAULT 1 COMMENT '通知声音开关',
ADD COLUMN do_not_disturb_start TIME DEFAULT '22:00:00' COMMENT '免打扰开始时间',
ADD COLUMN do_not_disturb_end TIME DEFAULT '08:00:00' COMMENT '免打扰结束时间';

-- 3. Create user_push_records table for tracking push notification history
CREATE TABLE IF NOT EXISTS user_push_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT,
    notice_id VARCHAR(100),
    push_type ENUM('wechat', 'email', 'sms', 'app') NOT NULL,
    push_content TEXT,
    push_title VARCHAR(255),
    push_status ENUM('pending', 'sent', 'delivered', 'failed') DEFAULT 'pending',
    push_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivery_time TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_subscription_id (subscription_id),
    INDEX idx_push_time (push_time),
    INDEX idx_push_status (push_status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户推送记录表';

-- 4. Create user_subscription_matches table for tracking matched notices
CREATE TABLE IF NOT EXISTS user_subscription_matches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT NOT NULL,
    notice_id VARCHAR(100) NOT NULL,
    notice_title VARCHAR(500),
    match_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '匹配分数',
    matched_keywords JSON COMMENT '匹配的关键词',
    matched_regions JSON COMMENT '匹配的地区',
    is_pushed TINYINT(1) DEFAULT 0 COMMENT '是否已推送',
    push_time TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_subscription (user_id, subscription_id),
    INDEX idx_notice_id (notice_id),
    INDEX idx_is_pushed (is_pushed),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    UNIQUE KEY unique_subscription_notice (subscription_id, notice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户订阅匹配记录表';

-- 5. Create regions table for comprehensive regional data
CREATE TABLE IF NOT EXISTS regions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    parent_code VARCHAR(20),
    level TINYINT NOT NULL COMMENT '级别：1-省份，2-城市，3-区县',
    sort_order INT DEFAULT 0,
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_parent_code (parent_code),
    INDEX idx_level (level),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地区数据表';

-- 6. Add user membership information to users table
ALTER TABLE users 
ADD COLUMN membership_type ENUM('free', 'vip', 'premium') DEFAULT 'free' COMMENT '会员类型',
ADD COLUMN membership_expires_at TIMESTAMP NULL COMMENT '会员到期时间',
ADD COLUMN subscription_limit INT DEFAULT 3 COMMENT '订阅数量限制',
ADD COLUMN daily_push_limit INT DEFAULT 50 COMMENT '每日推送数量限制';

-- 7. Create user_membership_history table for tracking membership changes
CREATE TABLE IF NOT EXISTS user_membership_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    old_membership_type ENUM('free', 'vip', 'premium'),
    new_membership_type ENUM('free', 'vip', 'premium') NOT NULL,
    change_reason VARCHAR(255),
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会员变更历史表';

-- 8. Create push_notification_queue table for managing push notifications
CREATE TABLE IF NOT EXISTS push_notification_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    subscription_id INT,
    notice_id VARCHAR(100),
    push_type ENUM('wechat', 'email', 'sms', 'app') NOT NULL,
    push_content TEXT NOT NULL,
    push_title VARCHAR(255) NOT NULL,
    scheduled_time TIMESTAMP NOT NULL,
    priority TINYINT DEFAULT 5 COMMENT '优先级：1-最高，5-最低',
    status ENUM('pending', 'processing', 'sent', 'failed') DEFAULT 'pending',
    retry_count INT DEFAULT 0,
    max_retries INT DEFAULT 3,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES user_subscriptions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='推送通知队列表';

-- 9. Create user_favorites table for user favorites
CREATE TABLE IF NOT EXISTS user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notice_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_notice_id (notice_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notice (user_id, notice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 10. Create user_browsing_history table for browsing history
CREATE TABLE IF NOT EXISTS user_browsing_history (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notice_id VARCHAR(100) NOT NULL,
    view_count INT DEFAULT 1 COMMENT '浏览次数',
    first_viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '首次浏览时间',
    last_viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后浏览时间',
    INDEX idx_user_id (user_id),
    INDEX idx_notice_id (notice_id),
    INDEX idx_last_viewed_at (last_viewed_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notice (user_id, notice_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户浏览记录表';

-- 11. Create user_feedback table for user feedback and suggestions
CREATE TABLE IF NOT EXISTS user_feedback (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NULL,
    type ENUM('suggestion', 'bug', 'complaint', 'praise', 'other') NOT NULL DEFAULT 'suggestion' COMMENT '反馈类型',
    title VARCHAR(255) NOT NULL COMMENT '反馈标题',
    content TEXT NOT NULL COMMENT '反馈内容',
    contact_info VARCHAR(255) NULL COMMENT '联系方式',
    rating TINYINT DEFAULT 5 CHECK (rating >= 1 AND rating <= 5) COMMENT '评分',
    is_anonymous BOOLEAN DEFAULT FALSE COMMENT '是否匿名',
    status ENUM('submitted', 'received') DEFAULT 'submitted' COMMENT '提交状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户反馈表';

-- 12. Create user_downloads table for user download records
CREATE TABLE IF NOT EXISTS user_downloads (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notice_id VARCHAR(100) NOT NULL COMMENT '公告ID',
    notice_title VARCHAR(500) NULL COMMENT '公告标题',
    notice_type VARCHAR(50) NULL COMMENT '公告类型',
    file_name VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size VARCHAR(50) NULL COMMENT '文件大小',
    file_url VARCHAR(500) NOT NULL COMMENT '文件URL',
    site_name VARCHAR(255) NULL COMMENT '来源网站',
    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
    INDEX idx_user_id (user_id),
    INDEX idx_notice_id (notice_id),
    INDEX idx_download_time (download_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户下载记录表';
